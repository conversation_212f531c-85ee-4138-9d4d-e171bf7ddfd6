@echo off
echo Setting up KLC Village Council Website...

REM Create and activate virtual environment if it doesn't exist
if not exist venv (
    echo Creating virtual environment...
    python -m venv venv
)

REM Activate virtual environment
call venv\Scripts\activate

REM Install requirements
echo Installing requirements...
pip install -r requirements.txt

REM Run setup script
echo Running setup script...
python setup_local.py

REM Collect static files
echo Collecting static files...
python manage.py collectstatic --noinput

REM Run migrations
echo Running migrations...
python manage.py migrate

REM Start the server
echo Starting development server...
python manage.py runserver

pause 