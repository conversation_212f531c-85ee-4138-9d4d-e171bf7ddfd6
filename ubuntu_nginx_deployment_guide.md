# KLC Django Deployment Guide for Ubuntu with Nginx and HTTPS

This guide will walk you through deploying your KLC Django application on Ubuntu with Nginx and HTTPS.

## Prerequisites

- Ubuntu server (18.04 or newer)
- SSH access to the server
- Domain name or IP address (*********** in this case)
- Basic knowledge of Linux commands

## Step 1: Prepare Your Server

1. Update your system packages:
   ```bash
   sudo apt update
   sudo apt upgrade -y
   ```

2. Install required packages:
   ```bash
   sudo apt install -y python3-pip python3-venv nginx
   ```

## Step 2: Transfer Your Project Files

1. Clone your repository or transfer your project files to the server:
   ```bash
   # If using git
   git clone <repository-url> /home/<USER>/KLC_django
   
   # Or use SCP to transfer files from your local machine
   scp -r /path/to/local/KLC_django ubuntu@***********:/home/<USER>/
   ```

2. Set up proper permissions:
   ```bash
   sudo chown -R ubuntu:ubuntu /home/<USER>/KLC_django
   ```

## Step 3: Set Up Python Environment

1. Navigate to your project directory:
   ```bash
   cd /home/<USER>/KLC_django
   ```

2. Create and activate a virtual environment:
   ```bash
   python3 -m venv venv
   source venv/bin/activate
   ```

3. Install required packages:
   ```bash
   pip install -r requirements.txt
   pip install gunicorn
   ```

## Step 4: Configure Django Settings

1. Update your Django settings for production:
   ```bash
   # Edit settings.py to update ALLOWED_HOSTS
   nano KLC/KLC/settings.py
   ```

2. Make sure the following settings are properly configured:
   ```python
   DEBUG = False
   ALLOWED_HOSTS = ['***********', 'localhost', '127.0.0.1']
   
   # For HTTPS
   SECURE_SSL_REDIRECT = True
   SESSION_COOKIE_SECURE = True
   CSRF_COOKIE_SECURE = True
   ```

3. Collect static files:
   ```bash
   python manage.py collectstatic --noinput
   ```

## Step 5: Set Up Gunicorn as a Service

1. Create a systemd service file:
   ```bash
   sudo nano /etc/systemd/system/klc-django.service
   ```

2. Add the following content:
   ```ini
   [Unit]
   Description=KLC Django Application
   After=network.target

   [Service]
   User=ubuntu
   Group=ubuntu
   WorkingDirectory=/home/<USER>/KLC_django
   ExecStart=/home/<USER>/KLC_django/venv/bin/gunicorn --workers 3 --bind 127.0.0.1:8000 KLC.wsgi:application
   Restart=on-failure

   [Install]
   WantedBy=multi-user.target
   ```

3. Enable and start the service:
   ```bash
   sudo systemctl daemon-reload
   sudo systemctl enable klc-django.service
   sudo systemctl start klc-django.service
   ```

4. Check the status to ensure it's running:
   ```bash
   sudo systemctl status klc-django.service
   ```

## Step 6: Configure Nginx

1. Create an Nginx configuration file:
   ```bash
   sudo nano /etc/nginx/sites-available/klc
   ```

2. Add the following configuration for HTTP:
   ```nginx
   server {
       listen 80;
       server_name ***********;
       
       location /static/ {
           alias /home/<USER>/KLC_django/staticfiles/;
           expires 30d;
           add_header Cache-Control "public, max-age=2592000";
       }
       
       location /media/ {
           alias /home/<USER>/KLC_django/media/;
           expires 30d;
           add_header Cache-Control "public, max-age=2592000";
       }
       
       location / {
           proxy_pass http://127.0.0.1:8000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }
       
       # Additional security headers
       add_header X-Content-Type-Options nosniff;
       add_header X-Frame-Options DENY;
       add_header X-XSS-Protection "1; mode=block";
   }
   ```

3. Enable the site and test the configuration:
   ```bash
   sudo ln -sf /etc/nginx/sites-available/klc /etc/nginx/sites-enabled/
   sudo rm /etc/nginx/sites-enabled/default  # Remove default site
   sudo nginx -t  # Test configuration
   sudo systemctl restart nginx  # Restart Nginx
   ```

## Step 7: Set Up HTTPS (Optional but Recommended)

1. Generate self-signed SSL certificates:
   ```bash
   sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
       -keyout /etc/ssl/private/klc-selfsigned.key \
       -out /etc/ssl/certs/klc-selfsigned.crt \
       -subj "/CN=***********"
   ```

2. Update Nginx configuration for HTTPS:
   ```bash
   sudo nano /etc/nginx/sites-available/klc
   ```

3. Replace the content with:
   ```nginx
   server {
       listen 80;
       server_name ***********;
       return 301 https://$host$request_uri;
   }

   server {
       listen 443 ssl;
       server_name ***********;
       
       ssl_certificate /etc/ssl/certs/klc-selfsigned.crt;
       ssl_certificate_key /etc/ssl/private/klc-selfsigned.key;
       ssl_protocols TLSv1.2 TLSv1.3;
       ssl_ciphers HIGH:!aNULL:!MD5;
       ssl_prefer_server_ciphers on;
       ssl_session_cache shared:SSL:10m;
       
       location /static/ {
           alias /home/<USER>/KLC_django/staticfiles/;
           expires 30d;
           add_header Cache-Control "public, max-age=2592000";
       }
       
       location /media/ {
           alias /home/<USER>/KLC_django/media/;
           expires 30d;
           add_header Cache-Control "public, max-age=2592000";
       }
       
       location / {
           proxy_pass http://127.0.0.1:8000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }
       
       # Additional security headers
       add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
       add_header X-Content-Type-Options nosniff;
       add_header X-Frame-Options DENY;
       add_header X-XSS-Protection "1; mode=block";
   }
   ```

4. Test and restart Nginx:
   ```bash
   sudo nginx -t
   sudo systemctl restart nginx
   ```

## Step 8: Configure Firewall

1. Allow HTTP and HTTPS traffic:
   ```bash
   sudo ufw allow 80
   sudo ufw allow 443
   ```

2. Enable the firewall if not already enabled:
   ```bash
   sudo ufw enable
   ```

## Step 9: Automated Setup

For convenience, we've provided an automated setup script (`setup_ubuntu_nginx.sh`) that performs all these steps. To use it:

1. Make the script executable:
   ```bash
   chmod +x setup_ubuntu_nginx.sh
   ```

2. Run the script as root:
   ```bash
   sudo ./setup_ubuntu_nginx.sh
   ```

## Troubleshooting

### Check Logs

- Nginx logs:
  ```bash
  sudo tail -f /var/log/nginx/error.log
  sudo tail -f /var/log/nginx/access.log
  ```

- Django application logs:
  ```bash
  sudo journalctl -u klc-django.service
  ```

### Common Issues

1. **502 Bad Gateway**: Usually means Gunicorn is not running or not accessible.
   - Check if Gunicorn is running: `sudo systemctl status klc-django.service`
   - Check if the port is correct in the Nginx configuration

2. **Static files not loading**: Check paths in Nginx configuration and permissions.
   - Ensure the paths in the Nginx configuration match your actual file paths
   - Check permissions: `sudo chown -R ubuntu:ubuntu /home/<USER>/KLC_django`

3. **Permission issues**: Make sure the user running the application has the necessary permissions.
   - Check and fix permissions: `sudo chown -R ubuntu:ubuntu /home/<USER>/KLC_django`

4. **SSL certificate issues**: Verify the certificate paths and permissions.
   - Check certificate paths in Nginx configuration
   - Ensure certificates are readable by Nginx: `sudo chmod 644 /etc/ssl/certs/klc-selfsigned.crt`

## Maintenance

### Restarting Services

- Restart Django application:
  ```bash
  sudo systemctl restart klc-django.service
  ```

- Restart Nginx:
  ```bash
  sudo systemctl restart nginx
  ```

### Updating the Application

1. Pull the latest changes:
   ```bash
   cd /home/<USER>/KLC_django
   git pull
   ```

2. Activate the virtual environment and update dependencies:
   ```bash
   source venv/bin/activate
   pip install -r requirements.txt
   ```

3. Collect static files and restart the service:
   ```bash
   python manage.py collectstatic --noinput
   sudo systemctl restart klc-django.service
   ```
