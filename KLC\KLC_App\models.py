from django.db import models
#models
#Person model for citizens
# class Person(models.Model):
#     #Attributes
#     national_id = models.Char<PERSON>ield(max_length=100)
#     name = models.CharField(max_length=1000)
#     phone = models.CharField(max_length=15)
#     address = models.CharField(max_length=100)
#     date_added = models.DateTimeField(auto_now_add=True)

#     def __str__(self):
#         return self.national_id