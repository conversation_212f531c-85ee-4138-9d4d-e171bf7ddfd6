{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">

    <!-- Force light mode by default -->
    <script>
      // Set light mode immediately before any other scripts run
      document.documentElement.setAttribute('data-theme', 'light');
      localStorage.setItem('theme', 'light');
    </script>

    <title>{% block title %}لوحة التحكم{% endblock %}</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/main.css' %}?v={{ STATIC_VERSION }}">
    <!-- Enhanced Dark Mode CSS -->
    <link rel="stylesheet" href="{% static 'css/dark_mode_enhanced.css' %}?v={{ STATIC_VERSION }}">
    <!-- Professional Admin UI CSS -->
    <link rel="stylesheet" href="{% static 'css/professional_admin.css' %}?v={{ STATIC_VERSION }}">

    {% block extra_css %}{% endblock %}
        <style>
        body {
            font-family: 'Cairo', 'Tajawal', 'Noto Kufi Arabic', sans-serif;
            background: linear-gradient(135deg, #f6f8fa 0%, #e9efff 100%);
            min-height: 100vh;
            transition: background 0.3s;
        }
        .sidebar {
            background: #22304a;
            min-height: 100vh;
            color: #fff;
            box-shadow: 2px 0 12px rgba(30,41,59,0.08);
        }
        .sidebar .navbar-brand {
            font-weight: bold;
            font-size: 1.3rem;
            color: #fff;
        }
        .sidebar .nav-link {
            color: #b6c3d6;
            border-radius: 8px;
            margin-bottom: 6px;
            transition: background 0.2s, color 0.2s;
        }
        .sidebar .nav-link.active, .sidebar .nav-link:hover {
            background: linear-gradient(90deg, #2563eb 60%, #1e40af 100%);
            color: #fff;
        }
        .sidebar .nav-link i {
            margin-left: 8px;
        }
        .main-content {
            background: #f9fbfd;
            border-radius: 18px;
            box-shadow: 0 4px 24px rgba(30,41,59,0.07);
            padding: 32px 24px;
            margin: 32px 0;
            transition: background 0.3s;
        }
        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 24px;
        }
        .section-header h2 {
            font-weight: 700;
            color: #2563eb;
        }
        .btn-primary, .btn-info, .btn-success, .btn-danger, .btn-warning {
            border-radius: 8px;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(37,99,235,0.08);
            transition: background 0.2s, box-shadow 0.2s;
        }
        .btn-primary {
            background: linear-gradient(90deg, #2563eb 60%, #1e40af 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(90deg, #1d4ed8 60%, #1e40af 100%);
        }
        .btn-info {
            background: linear-gradient(90deg, #0ea5e9 60%, #0369a1 100%);
            border: none;
        }
        .btn-info:hover {
            background: linear-gradient(90deg, #0284c7 60%, #0369a1 100%);
        }
        .btn-success {
            background: linear-gradient(90deg, #22c55e 60%, #15803d 100%);
            border: none;
        }
        .btn-success:hover {
            background: linear-gradient(90deg, #16a34a 60%, #15803d 100%);
        }
        .btn-danger {
            background: linear-gradient(90deg, #ef4444 60%, #991b1b 100%);
            border: none;
        }
        .btn-danger:hover {
            background: linear-gradient(90deg, #b91c1c 60%, #991b1b 100%);
        }
        .btn-warning {
            background: linear-gradient(90deg, #f59e42 60%, #b45309 100%);
            border: none;
        }
        .btn-warning:hover {
            background: linear-gradient(90deg, #d97706 60%, #b45309 100%);
        }
        .card {
            border-radius: 16px;
            box-shadow: 0 2px 12px rgba(30,41,59,0.06);
            border: none;
            background: #fff;
            transition: background 0.3s, box-shadow 0.3s;
        }
        .card .card-body {
            padding: 1.5rem;
        }
        .table {
            border-radius: 12px;
            overflow: hidden;
            font-family: 'Tajawal', 'Cairo', 'Noto Kufi Arabic', sans-serif;
            font-size: 1.08rem;
            background: #fff;
        }
        .table thead {
            background: #2563eb;
            color: #fff;
            font-size: 1.13rem;
            font-weight: 700;
            letter-spacing: 0.5px;
        }
        .table th, .table td {
            vertical-align: middle;
            padding: 1rem 0.75rem;
        }
        .table tbody tr {
            transition: background 0.2s;
        }
        .table tbody tr:nth-child(even) {
            background: #f3f6fb;
        }
        .table tbody tr:hover {
            background: #e0e7ef;
        }
        .badge {
            font-size: 1em;
            border-radius: 6px;
            padding: 0.5em 1em;
        }
        .alert {
            border-radius: 10px;
        }
        .theme-toggle {
            position: fixed;
            bottom: 32px;
            left: 32px;
            background: #2563eb;
            color: #fff;
            border: none;
            border-radius: 50%;
            width: 48px;
            height: 48px;
            box-shadow: 0 2px 8px rgba(37,99,235,0.12);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            z-index: 999;
            transition: background 0.2s;
        }
        .theme-toggle:hover {
            background: #1d4ed8;
        }
        /* Top cards (dashboard summary) */
        .card.shadow-sm.h-100 {
            background: linear-gradient(135deg, #f3f6fb 60%, #e0e7ef 100%);
            border: none;
            box-shadow: 0 2px 16px rgba(37,99,235,0.07);
            transition: background 0.3s, color 0.3s;
        }
        .card .fa-2x {
            filter: drop-shadow(0 2px 6px rgba(37,99,235,0.10));
        }
        /* Dark mode styles */
        .dark-mode body {
            background: linear-gradient(135deg, #181f2a 0%, #232b3e 100%);
        }
        .dark-mode .sidebar {
            background: #151a23;
            color: #e0e7ef;
        }
        .dark-mode .sidebar .nav-link {
            color: #8ca0c3;
        }
        .dark-mode .sidebar .nav-link.active, .dark-mode .sidebar .nav-link:hover {
            background: linear-gradient(90deg, #2563eb 60%, #1e40af 100%);
            color: #fff;
        }
        .dark-mode .main-content {
            background: #232b3e;
            color: #e0e7ef;
        }
        .dark-mode .section-header h2 {
            color: #60a5fa;
        }
        .dark-mode .card {
            background: linear-gradient(135deg, #232b3e 60%, #181f2a 100%);
            color: #e0e7ef;
            box-shadow: 0 2px 16px rgba(37,99,235,0.13);
        }
        .dark-mode .card.shadow-sm.h-100 {
            background: #181a20 !important;
            color: #fff !important;
            box-shadow: 0 2px 16px rgba(0,0,0,0.25);
            border: 1px solid #232b3e;
        }
        .dark-mode .card.shadow-sm.h-100 * {
            color: #fff !important;
        }
        .dark-mode .card.shadow-sm.h-100 .rounded-circle {
            background: #232b3e !important;
        }
        .dark-mode .card.shadow-sm.h-100 .fa-2x {
            color: #38bdf8 !important;
        }
        .dark-mode .card.shadow-sm.h-100 .text-primary,
        .dark-mode .card.shadow-sm.h-100 .text-success,
        .dark-mode .card.shadow-sm.h-100 .text-danger,
        .dark-mode .card.shadow-sm.h-100 .text-info {
            color: #fff !important;
            opacity: 0.95;
        }
        .dark-mode .card.shadow-sm.h-100 h3,
        .dark-mode .card.shadow-sm.h-100 h6 {
            color: #fff !important;
        }
        .dark-mode .table {
            background: #232b3e;
            color: #fff;
        }
        .dark-mode .table thead {
            background: #1e40af;
            color: #fff;
        }
        .dark-mode .table tbody tr:nth-child(even) {
            background: #232b3e;
        }
        .dark-mode .table tbody tr:nth-child(odd) {
            background: #181a20;
        }
        .dark-mode .table tbody tr:hover {
            background: #2563eb;
            color: #fff;
        }
        .dark-mode .btn-primary {
            background: linear-gradient(90deg, #2563eb 60%, #1e40af 100%);
        }
        .dark-mode .btn-info {
            background: linear-gradient(90deg, #0ea5e9 60%, #0369a1 100%);
        }
        .dark-mode .btn-success {
            background: linear-gradient(90deg, #22c55e 60%, #15803d 100%);
        }
        .dark-mode .btn-danger {
            background: linear-gradient(90deg, #ef4444 60%, #991b1b 100%);
        }
        .dark-mode .btn-warning {
            background: linear-gradient(90deg, #f59e42 60%, #b45309 100%);
        }
        .dark-mode .alert {
            background: #232b3e;
            color: #fbbf24;
        }
        .dark-mode .theme-toggle {
            background: #2563eb;
            color: #fff;
        }
        .dark-mode .card.shadow-sm.h-100,
        .dark-mode .card.shadow-sm.h-100 *,
        .dark-mode .card.shadow-sm.h-100 .fa-2x,
        .dark-mode .card.shadow-sm.h-100 .card-title,
        .dark-mode .card.shadow-sm.h-100 .card-subtitle {
            color: #fff !important;
        }
        .dark-mode .card.shadow-sm.h-100 .rounded-circle {
            background: linear-gradient(135deg, #232b3e 60%, #181a20 100%) !important;
            box-shadow: 0 2px 8px rgba(96,165,250,0.10);
        }
        .dark-mode .card.shadow-sm.h-100 .fa-2x {
            color: #38bdf8 !important;
        }
        .card.shadow-sm.h-100 h3.card-title {
            font-family: 'Tajawal', 'Cairo', 'Noto Kufi Arabic', sans-serif;
            font-size: 2.2rem;
            font-weight: 700;
            letter-spacing: 0.5px;
            line-height: 1.2;
            margin-bottom: 0;
        }
        .card.shadow-sm.h-100 h6.card-subtitle {
            font-family: 'Tajawal', 'Cairo', 'Noto Kufi Arabic', sans-serif;
            font-size: 1.1rem;
            font-weight: 600;
            letter-spacing: 0.2px;
            color: #64748b;
        }
        .dark-mode .card.shadow-sm.h-100 h3.card-title,
        .dark-mode .card.shadow-sm.h-100 h6.card-subtitle {
            color: #fff !important;
            text-shadow: 0 1px 2px rgba(0,0,0,0.12);
        }
        /* DataTables control area styling */
        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter {
            font-family: 'Tajawal', 'Cairo', 'Noto Kufi Arabic', sans-serif;
            font-size: 1.08rem;
            margin-bottom: 0;
        }
        .dataTables_wrapper .dataTables_length {
            float: right;
            margin-left: 1.5rem;
        }
        .dataTables_wrapper .dataTables_filter {
            float: left;
            margin-right: 1.5rem;
        }
        .dataTables_wrapper .dataTables_length label,
        .dataTables_wrapper .dataTables_filter label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: #f3f6fb;
            border-radius: 8px;
            padding: 0.5rem 1rem;
            box-shadow: 0 1px 4px rgba(30,41,59,0.06);
        }
        .dataTables_wrapper .dataTables_length select,
        .dataTables_wrapper .dataTables_filter input {
            font-size: 1.08rem;
            border-radius: 6px;
            border: 1px solid #d1d5db;
            padding: 0.3rem 0.7rem;
            margin: 0 0.2rem;
            background: #fff;
            transition: border 0.2s;
        }
        .dataTables_wrapper .dataTables_filter input:focus,
        .dataTables_wrapper .dataTables_length select:focus {
            border: 1.5px solid #2563eb;
            outline: none;
        }
        /* Responsive gap for controls */
        @media (max-width: 768px) {
            .dataTables_wrapper .dataTables_length,
            .dataTables_wrapper .dataTables_filter {
                float: none;
                margin: 0.5rem 0;
                width: 100%;
                justify-content: flex-start;
            }
            .dataTables_wrapper .dataTables_length label,
            .dataTables_wrapper .dataTables_filter label {
                width: 100%;
                justify-content: flex-start;
            }
        }
        /* Dark mode for DataTables controls */
        .dark-mode .dataTables_wrapper .dataTables_length label,
        .dark-mode .dataTables_wrapper .dataTables_filter label {
            background: #232b3e;
            color: #fff;
            box-shadow: 0 1px 4px rgba(37,99,235,0.10);
        }
        .dark-mode .dataTables_wrapper .dataTables_length select,
        .dark-mode .dataTables_wrapper .dataTables_filter input {
            background: #181a20;
            color: #fff;
            border: 1px solid #334155;
        }
        .dark-mode .dataTables_wrapper .dataTables_filter input:focus,
        .dark-mode .dataTables_wrapper .dataTables_length select:focus {
            border: 1.5px solid #60a5fa;
        }
        /* DataTables RTL control area as flex */
        html[dir="rtl"] .dataTables_wrapper .dataTables_filter,
        html[dir="rtl"] .dataTables_wrapper .dataTables_length {
            float: none !important;
            margin: 0 !important;
        }
        html[dir="rtl"] .dataTables_wrapper .dataTables_filter label,
        html[dir="rtl"] .dataTables_wrapper .dataTables_length label {
            margin: 0;
        }
        html[dir="rtl"] .dataTables_wrapper .dataTables_filter,
        html[dir="rtl"] .dataTables_wrapper .dataTables_length {
            display: flex;
            align-items: center;
        }
        html[dir="rtl"] .dataTables_wrapper .dt-top-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            margin-bottom: 1rem;
            gap: 1rem;
        }
        @media (max-width: 768px) {
            html[dir="rtl"] .dataTables_wrapper .dt-top-row {
                flex-direction: column;
                align-items: stretch;
                gap: 0.5rem;
            }
        }
        @media (max-width: 991px) {
            .main-content {
                padding: 16px 6px;
                margin: 16px 0;
            }
            .section-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.7rem;
            }
        }
        @media (max-width: 768px) {
            .row.g-4.mb-4 > div[class^="col-"] {
                flex: 0 0 100%;
                max-width: 100%;
                margin-bottom: 1rem;
            }
            .row.g-4.mb-4 {
                flex-direction: column;
                gap: 0.7rem;
            }
            .main-content {
                padding: 8px 2px;
                margin: 8px 0;
            }
            .card.shadow-sm.h-100 {
                min-width: 0;
                margin-bottom: 1rem;
            }
            .section-header h2 {
                font-size: 1.2rem;
            }
            .btn, .btn-primary, .btn-info, .btn-success, .btn-danger, .btn-warning {
                font-size: 1rem;
                padding: 0.5rem 1rem;
            }
            .sidebar {
                min-height: auto;
                padding-bottom: 1rem;
            }
            .sidebar .navbar-brand img {
                height: 32px !important;
            }
        }
        /* Responsive table scroll */
        .table-responsive {
            overflow-x: auto;
            border-radius: 12px;
            box-shadow: 0 1px 6px rgba(30,41,59,0.06);
        }
        .table {
            min-width: 600px;
        }
        @media (max-width: 600px) {
            .table {
                font-size: 0.97rem;
            }
            .table th, .table td {
                padding: 0.7rem 0.4rem;
            }
            .card.shadow-sm.h-100 h3.card-title {
                font-size: 1.3rem;
            }
        }
        /* DataTables controls mobile */
        @media (max-width: 600px) {
            .dataTables_wrapper .dataTables_length label,
            .dataTables_wrapper .dataTables_filter label {
                font-size: 0.97rem;
                padding: 0.4rem 0.7rem;
            }
            .dataTables_wrapper .dataTables_length select,
            .dataTables_wrapper .dataTables_filter input {
                font-size: 0.97rem;
                padding: 0.2rem 0.5rem;
            }
        }
    </style>
</head>
<body class="d-flex flex-column min-vh-100">



<!-- Main Content -->
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar">
            <div class="position-sticky">
                <div class="navbar-brand mb-4">
                    <img src="{% static 'images/logo.png' %}" alt="Logo" class="me-2" style="height: 45px;">
                    لوحة الإدارة
                </div>
            </div>
        </nav>


        <!-- Content Area -->
        <main class="col-md-9 ms-sm-auto col-lg-10 p-4">
            <!-- Messages -->
            {% if messages %}
            <div class="messages-container">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            {% block content %}{% endblock %}
        </main>
    </div>
</div>

{% include 'KLC_App/footer.html' %}

<!-- Scripts -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
{% block extra_js %}{% endblock %}

</body>
</html>