{% load static %}
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Admin <PERSON> - <PERSON>r Ein Local Council</title>

    <link
      rel="icon"
      type="image/x-icon"
      href="{% static 'images/logo.png' %}"
    />

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH"
      crossorigin="anonymous"
    />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'css/style.css' %}" rel="stylesheet" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{% static 'images/logo.png' %}" />

    <style>
      body {
        font-family: 'Poppins', sans-serif;
        background-image: url('{% static "images/header-background.jpg" %}');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
        height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1;
      }

      .login-container {
        position: relative;
        z-index: 10;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        width: 100%;
        max-width: 500px;
        padding: 40px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.18);
      }

      .logo {
        max-height: 120px;
        transition: transform 0.3s ease;
      }

      .logo:hover {
        transform: scale(1.05);
      }

      h2 {
        color: #1a73e8;
        font-weight: 600;
        margin-bottom: 25px;
        letter-spacing: 0.5px;
      }

      .form-control {
        background-color: #f8f9fa;
        border: 1px solid #dfe1e5;
        border-radius: 10px;
        padding: 12px 15px;
        font-size: 16px;
        transition: all 0.3s ease;
        box-shadow: none !important;
      }

      .form-control:focus {
        border-color: #1a73e8;
        background-color: #fff;
      }

      .input-group {
        margin-bottom: 20px;
      }

      .input-group-text {
        background-color: #f8f9fa;
        border: 1px solid #dfe1e5;
        border-right: none;
        color: #5f6368;
        border-radius: 10px 0 0 10px;
      }

      .btn-primary {
        background: linear-gradient(135deg, #1a73e8, #0d5bdd);
        border: none;
        border-radius: 10px;
        padding: 12px 0;
        font-weight: 500;
        letter-spacing: 0.5px;
        box-shadow: 0 4px 6px rgba(26, 115, 232, 0.2);
        transition: all 0.3s ease;
      }

      .btn-primary:hover {
        background: linear-gradient(135deg, #0d5bdd, #1a73e8);
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(26, 115, 232, 0.3);
      }

      .btn-primary:active {
        transform: translateY(0);
      }

      .footer-text {
        font-size: 14px;
        color: #5f6368;
        margin-top: 25px;
      }

      .alert {
        border-radius: 10px;
        padding: 12px 15px;
        margin-bottom: 20px;
        border: none;
      }

      @media (max-width: 576px) {
        .login-container {
          max-width: 90%;
          padding: 25px 15px;
          border-radius: 15px;
        }

        .logo {
          max-height: 70px;
        }

        h2 {
          font-size: 1.6rem;
          margin-bottom: 20px;
        }

        .form-control {
          padding: 10px 12px;
          font-size: 15px;
        }

        .input-group-text {
          padding: 10px 12px;
        }

        .btn-primary {
          padding: 10px 0;
        }

        .footer-text {
          font-size: 12px;
          margin-top: 20px;
        }

        .alert {
          padding: 10px 12px;
          margin-bottom: 15px;
          font-size: 14px;
        }
      }

      /* Extra small devices */
      @media (max-width: 360px) {
        .login-container {
          padding: 20px 12px;
        }

        .logo {
          max-height: 60px;
        }

        h2 {
          font-size: 1.4rem;
          margin-bottom: 15px;
        }

        .form-control, .input-group-text {
          padding: 8px 10px;
          font-size: 14px;
        }

        .btn-primary {
          padding: 8px 0;
          font-size: 14px;
        }
      }
    </style>
  </head>

  <body>
    <div class="overlay"></div>

    <!-- Container for Logo and Admin Login -->
    <div class="login-container text-center">
      <!-- Logo -->
      <img
        src="{% static 'images/logo.png' %}"
        alt="Kafr Ein Local Council Logo"
        class="logo img-fluid mb-3"
      />

      <!-- Title -->
      <h2>KLC Admin Login</h2>

      {% if error %}
      <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i> {{ error }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
      </div>
      {% endif %}

      <!-- Login Form -->
      <form method="post" class="text-start">
        {% csrf_token %}
        <div class="input-group mb-3">
          <span class="input-group-text">
            <i class="fas fa-user"></i>
          </span>
          <input
            type="text"
            name="username"
            placeholder="Username"
            class="form-control"
            required
            autocomplete="username"
          />
        </div>

        <div class="input-group mb-4">
          <span class="input-group-text">
            <i class="fas fa-lock"></i>
          </span>
          <input
            type="password"
            name="password"
            placeholder="Password"
            class="form-control"
            required
            autocomplete="current-password"
          />
        </div>

        <button
          type="submit"
          class="btn btn-primary w-100 mb-3"
        >
          <i class="fas fa-sign-in-alt me-2"></i> Login
        </button>

      </form>

      <p class="footer-text">© 2025 Kafr Ein Local Council. All rights reserved.</p>
    </div>

    <!-- Bootstrap JS -->
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
      integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz"
      crossorigin="anonymous"
    ></script>

    <!-- Optional JavaScript -->
    <script>
      // Add any custom JavaScript functionality here
      document.addEventListener('DOMContentLoaded', function() {
        // Focus on username field when page loads
        document.querySelector('input[name="username"]').focus();

        // Add subtle animation to form fields on focus
        const formControls = document.querySelectorAll('.form-control');
        formControls.forEach(control => {
          control.addEventListener('focus', function() {
            this.parentElement.style.transform = 'translateY(-2px)';
            this.parentElement.style.transition = 'transform 0.3s ease';
          });

          control.addEventListener('blur', function() {
            this.parentElement.style.transform = 'translateY(0)';
          });
        });
      });
    </script>
  </body>
</html>