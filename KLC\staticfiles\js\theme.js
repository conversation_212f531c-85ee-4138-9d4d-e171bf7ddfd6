// Enhanced Theme management
document.addEventListener('DOMContentLoaded', function() {
    const themeToggle = document.querySelector('.theme-toggle');
    if (!themeToggle) return; // Exit if theme toggle button doesn't exist

    const themeIcon = themeToggle.querySelector('i');

    // Check for saved theme preference or use system preference
    const prefersDarkScheme = window.matchMedia('(prefers-color-scheme: dark)');
    const savedTheme = localStorage.getItem('theme');

    // Set initial theme
    if (savedTheme) {
        document.documentElement.setAttribute('data-theme', savedTheme);
        updateThemeIcon(savedTheme);
    } else {
        // Always default to light mode regardless of system preference
        document.documentElement.setAttribute('data-theme', 'light');
        updateThemeIcon('light');
        // Save the light theme preference
        localStorage.setItem('theme', 'light');
    }

    // Theme toggle click handler
    themeToggle.addEventListener('click', () => {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        updateThemeIcon(newTheme);

        // Add a transition class to body for smooth theme change
        document.body.classList.add('theme-transition');
        setTimeout(() => {
            document.body.classList.remove('theme-transition');
        }, 1000);
    });

    // Update theme icon
    function updateThemeIcon(theme) {
        if (theme === 'dark') {
            themeIcon.className = 'fas fa-sun';
            themeToggle.title = 'التبديل إلى الوضع الفاتح';
            themeToggle.setAttribute('aria-label', 'التبديل إلى الوضع الفاتح');
        } else {
            themeIcon.className = 'fas fa-moon';
            themeToggle.title = 'التبديل إلى الوضع الداكن';
            themeToggle.setAttribute('aria-label', 'التبديل إلى الوضع الداكن');
        }
    }

    // Listen for system theme changes
    prefersDarkScheme.addEventListener('change', (e) => {
        // Only respond to system theme changes if the user hasn't explicitly set a theme
        if (!localStorage.getItem('theme')) {
            // Always use light theme as default
            document.documentElement.setAttribute('data-theme', 'light');
            updateThemeIcon('light');
            localStorage.setItem('theme', 'light');
        }
    });
});