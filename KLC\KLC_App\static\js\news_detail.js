// Enhanced Professional News Detail Modal Functionality
document.addEventListener("DOMContentLoaded", function () {
  // News Detail Modal
  const newsDetailModal = document.getElementById("newsDetailModal");
  if (newsDetailModal) {
    // Gallery variables
    let currentSlideIndex = 0;
    let galleryImages = [];
    let isFullscreen = false;

    // Enhanced Gallery navigation functions
    function showSlide(index) {
      // Hide all slides
      const slides = newsDetailModal.querySelectorAll('.news-gallery-slide');
      slides.forEach(slide => {
        slide.classList.remove('active');
      });

      // Show the selected slide with smooth transition
      if (slides[index]) {
        slides[index].classList.add('active');
      }

      // Update dots with active state
      const dots = newsDetailModal.querySelectorAll('.gallery-dot');
      dots.forEach((dot, i) => {
        dot.classList.toggle('active', i === index);
      });

      // Update thumbnails with active state and scroll into view
      const thumbnails = newsDetailModal.querySelectorAll('.gallery-thumbnail');
      thumbnails.forEach((thumb, i) => {
        thumb.classList.toggle('active', i === index);
        if (i === index) {
          // Scroll the active thumbnail into view
          setTimeout(() => {
            thumb.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
          }, 100);
        }
      });

      // Update image counter
      const currentImageIndex = newsDetailModal.querySelector('#currentImageIndex');
      if (currentImageIndex) {
        currentImageIndex.textContent = index + 1;
      }

      // Update current index
      currentSlideIndex = index;
    }

    function nextSlide() {
      if (galleryImages.length > 1) {
        let newIndex = currentSlideIndex + 1;
        if (newIndex >= galleryImages.length) {
          newIndex = 0;
        }
        showSlide(newIndex);
      }
    }

    function prevSlide() {
      if (galleryImages.length > 1) {
        let newIndex = currentSlideIndex - 1;
        if (newIndex < 0) {
          newIndex = galleryImages.length - 1;
        }
        showSlide(newIndex);
      }
    }

    // Toggle fullscreen mode
    function toggleFullscreen() {
      const galleryElement = newsDetailModal.querySelector('.news-gallery');
      if (!galleryElement) return;

      isFullscreen = !isFullscreen;

      if (isFullscreen) {
        galleryElement.classList.add('fullscreen');
        document.body.style.overflow = 'hidden'; // Prevent scrolling when in fullscreen
      } else {
        galleryElement.classList.remove('fullscreen');
        document.body.style.overflow = ''; // Restore scrolling
      }
    }

    // Set up gallery event listeners
    const galleryNext = newsDetailModal.querySelector('.gallery-next');
    const galleryPrev = newsDetailModal.querySelector('.gallery-prev');
    const fullscreenBtn = newsDetailModal.querySelector('.gallery-fullscreen');

    if (galleryNext) {
      galleryNext.addEventListener('click', nextSlide);
    }

    if (galleryPrev) {
      galleryPrev.addEventListener('click', prevSlide);
    }

    if (fullscreenBtn) {
      fullscreenBtn.addEventListener('click', toggleFullscreen);
    }

    // Add keyboard navigation
    newsDetailModal.addEventListener('keydown', function(e) {
      if (e.key === 'ArrowRight') {
        prevSlide(); // In RTL, right arrow moves backward
      } else if (e.key === 'ArrowLeft') {
        nextSlide(); // In RTL, left arrow moves forward
      } else if (e.key === 'Escape' && isFullscreen) {
        toggleFullscreen(); // Exit fullscreen on Escape key
      } else if (e.key === 'f' || e.key === 'F') {
        toggleFullscreen(); // Toggle fullscreen with F key
      }
    });

    // Enhanced Professional Modal show event
    newsDetailModal.addEventListener("show.bs.modal", function (event) {
      const button = event.relatedTarget;
      const newsTitle = button.getAttribute("data-news-title");
      const newsDesc = button.getAttribute("data-news-desc");
      const newsDate = button.getAttribute("data-news-date");
      const newsImage = button.getAttribute("data-news-image");
      const newsCategory = button.getAttribute("data-news-category");
      const newsVideo = button.getAttribute("data-news-video");

      // Reset fullscreen state
      isFullscreen = false;
      const galleryElement = newsDetailModal.querySelector('.news-gallery');
      if (galleryElement && galleryElement.classList.contains('fullscreen')) {
        galleryElement.classList.remove('fullscreen');
        document.body.style.overflow = '';
      }

      // Get additional images if available with enhanced error handling
      let additionalImages = [];
      try {
        const additionalImagesAttr = button.getAttribute("data-news-additional-images");
        if (additionalImagesAttr && additionalImagesAttr.trim() !== '') {
          additionalImages = JSON.parse(additionalImagesAttr);

          // Filter out any invalid image URLs
          additionalImages = additionalImages.filter(img => img && img.trim() !== '');
        }
      } catch (e) {
        console.error("Error parsing additional images:", e);
        additionalImages = [];
      }

      // Combine main image with additional images
      galleryImages = [newsImage];
      if (Array.isArray(additionalImages) && additionalImages.length > 0) {
        galleryImages = galleryImages.concat(additionalImages);
      }

      // Get all modal elements
      const modalTitle = newsDetailModal.querySelector("#newsDetailModalLabel");
      const modalImage = newsDetailModal.querySelector("#newsDetailImage");
      const modalDate = newsDetailModal.querySelector("#newsDetailDate");
      const modalCategory = newsDetailModal.querySelector("#newsDetailCategory");
      const modalDesc = newsDetailModal.querySelector("#newsDetailDescription");
      const videoContainer = newsDetailModal.querySelector("#newsDetailVideoContainer");
      const showVideoBtn = newsDetailModal.querySelector("#showVideoBtn");
      const galleryContainer = newsDetailModal.querySelector(".news-gallery");
      const galleryDots = newsDetailModal.querySelector("#galleryDots");
      const galleryThumbnails = newsDetailModal.querySelector("#galleryThumbnails");
      const currentImageIndex = newsDetailModal.querySelector("#currentImageIndex");
      const totalImagesCount = newsDetailModal.querySelector("#totalImagesCount");
      const galleryCounter = newsDetailModal.querySelector(".gallery-counter");
      const galleryFullscreen = newsDetailModal.querySelector(".gallery-fullscreen");

      // Reset gallery
      currentSlideIndex = 0;

      // Set modal content with professional styling
      if (modalTitle) modalTitle.textContent = newsTitle || '';

      // Update image counter
      if (currentImageIndex) currentImageIndex.textContent = '1';
      if (totalImagesCount) totalImagesCount.textContent = galleryImages.length.toString();

      // Show/hide counter and fullscreen button based on image count
      if (galleryCounter) {
        galleryCounter.style.display = galleryImages.length > 1 ? 'block' : 'none';
      }

      if (galleryFullscreen) {
        galleryFullscreen.style.display = galleryImages.length > 0 ? 'flex' : 'none';
      }

      // Set up enhanced gallery
      if (galleryContainer) {
        // Clear existing slides except the first one
        const existingSlides = galleryContainer.querySelectorAll('.news-gallery-slide:not(#singleImageSlide)');
        existingSlides.forEach(slide => slide.remove());

        // Set up the first slide (main image) with enhanced loading
        if (modalImage) {
          // Create a new image element to preload
          const preloadImg = new Image();
          preloadImg.onload = function() {
            modalImage.src = newsImage || '';
            modalImage.alt = newsTitle || '';
            modalImage.classList.add('loaded');
          };
          preloadImg.onerror = function() {
            // Fallback for image loading error
            modalImage.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIiBwcmVzZXJ2ZUFzcGVjdFJhdGlvPSJ4TWlkWU1pZCI+PHJlY3QgZmlsbD0iIzMzMyIgd2lkdGg9IjEwMCIgaGVpZ2h0PSIxMDAiLz48dGV4dCBmaWxsPSIjZmZmIiB4PSI1MCIgeT0iNTAiIGZvbnQtc2l6ZT0iMTAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGFsaWdubWVudC1iYXNlbGluZT0ibWlkZGxlIiBmb250LWZhbWlseT0ic2Fucy1zZXJpZiI+SW1hZ2UgTm90IEF2YWlsYWJsZTwvdGV4dD48L3N2Zz4=';
            modalImage.alt = 'Image Not Available';
          };
          preloadImg.src = newsImage || '';
        }

        // Add additional image slides with enhanced loading
        if (Array.isArray(additionalImages) && additionalImages.length > 0) {
          additionalImages.forEach((imgSrc, index) => {
            const slideDiv = document.createElement('div');
            slideDiv.className = 'news-gallery-slide';
            slideDiv.id = `slide-${index + 1}`;

            const img = document.createElement('img');

            // Create loading indicator
            const loadingIndicator = document.createElement('div');
            loadingIndicator.className = 'slide-loading-indicator';
            loadingIndicator.innerHTML = '<div class="spinner-border text-light" role="status"><span class="visually-hidden">Loading...</span></div>';
            slideDiv.appendChild(loadingIndicator);

            // Preload image
            const preloadImg = new Image();
            preloadImg.onload = function() {
              img.src = imgSrc;
              img.alt = `${newsTitle} - Image ${index + 1}`;
              slideDiv.removeChild(loadingIndicator);
            };
            preloadImg.onerror = function() {
              img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIiBwcmVzZXJ2ZUFzcGVjdFJhdGlvPSJ4TWlkWU1pZCI+PHJlY3QgZmlsbD0iIzMzMyIgd2lkdGg9IjEwMCIgaGVpZ2h0PSIxMDAiLz48dGV4dCBmaWxsPSIjZmZmIiB4PSI1MCIgeT0iNTAiIGZvbnQtc2l6ZT0iMTAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGFsaWdubWVudC1iYXNlbGluZT0ibWlkZGxlIiBmb250LWZhbWlseT0ic2Fucy1zZXJpZiI+SW1hZ2UgTm90IEF2YWlsYWJsZTwvdGV4dD48L3N2Zz4=';
              img.alt = 'Image Not Available';
              slideDiv.removeChild(loadingIndicator);
            };
            preloadImg.src = imgSrc;

            slideDiv.appendChild(img);
            galleryContainer.appendChild(slideDiv);
          });

          // Show gallery navigation with enhanced visibility
          const navButtons = newsDetailModal.querySelectorAll('.gallery-prev, .gallery-next');
          navButtons.forEach(btn => {
            btn.style.display = 'flex';
          });
        } else {
          // Hide gallery navigation if only one image
          const navButtons = newsDetailModal.querySelectorAll('.gallery-prev, .gallery-next');
          navButtons.forEach(btn => {
            btn.style.display = 'none';
          });
        }
      }

      // Set up enhanced gallery dots
      if (galleryDots) {
        galleryDots.innerHTML = '';

        if (galleryImages.length > 1) {
          galleryImages.forEach((_, index) => {
            const dot = document.createElement('div');
            dot.className = 'gallery-dot';
            if (index === 0) {
              dot.classList.add('active');
            }

            dot.addEventListener('click', () => {
              showSlide(index);
            });

            galleryDots.appendChild(dot);
          });
          galleryDots.style.display = 'flex';
        } else {
          galleryDots.style.display = 'none';
        }
      }

      // Set up enhanced thumbnails with preloading
      if (galleryThumbnails) {
        galleryThumbnails.innerHTML = '';

        if (galleryImages.length > 1) {
          galleryImages.forEach((imgSrc, index) => {
            const thumbnail = document.createElement('div');
            thumbnail.className = 'gallery-thumbnail';
            if (index === 0) {
              thumbnail.classList.add('active');
            }

            const img = document.createElement('img');

            // Preload thumbnail
            const preloadImg = new Image();
            preloadImg.onload = function() {
              img.src = imgSrc;
              img.alt = `Thumbnail ${index + 1}`;
              thumbnail.classList.add('loaded');
            };
            preloadImg.onerror = function() {
              img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIiBwcmVzZXJ2ZUFzcGVjdFJhdGlvPSJ4TWlkWU1pZCI+PHJlY3QgZmlsbD0iIzMzMyIgd2lkdGg9IjEwMCIgaGVpZ2h0PSIxMDAiLz48dGV4dCBmaWxsPSIjZmZmIiB4PSI1MCIgeT0iNTAiIGZvbnQtc2l6ZT0iMTAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGFsaWdubWVudC1iYXNlbGluZT0ibWlkZGxlIiBmb250LWZhbWlseT0ic2Fucy1zZXJpZiI+SW1hZ2UgTm90IEF2YWlsYWJsZTwvdGV4dD48L3N2Zz4=';
              img.alt = 'Thumbnail Not Available';
            };
            preloadImg.src = imgSrc;

            thumbnail.appendChild(img);
            thumbnail.addEventListener('click', () => {
              showSlide(index);
            });

            galleryThumbnails.appendChild(thumbnail);
          });
          galleryThumbnails.style.display = 'flex';
        } else {
          galleryThumbnails.style.display = 'none';
        }
      }

      // Set metadata with enhanced styling
      if (modalDate) modalDate.textContent = newsDate || '';
      if (modalCategory) modalCategory.textContent = newsCategory || '';
      if (modalDesc) {
        // Format description with proper paragraphs
        const formattedDesc = newsDesc ? newsDesc.replace(/\n\n/g, '</p><p>').replace(/\n/g, '<br>') : '';
        modalDesc.innerHTML = `<p>${formattedDesc}</p>`;
      }

      // Enhanced video button handling
      if (showVideoBtn) {
        if (newsVideo && newsVideo.trim() !== '') {
          showVideoBtn.style.display = 'inline-block';
          showVideoBtn.setAttribute('data-last-video', newsVideo);

          // Add enhanced click event to show video
          showVideoBtn.onclick = function() {
            let finalVideoSrc = newsVideo;

            // Process different video sources
            if (newsVideo.includes("drive.google.com")) {
              let fileId = "";
              if (newsVideo.includes("/file/d/")) {
                fileId = newsVideo.split("/file/d/")[1].split("/")[0];
              } else if (newsVideo.includes("id=")) {
                fileId = newsVideo.split("id=")[1].split("&")[0];
              }

              if (fileId) {
                finalVideoSrc = `https://drive.google.com/file/d/${fileId}/preview`;
              }
            }
            // Handle YouTube videos
            else if (newsVideo.includes("youtube.com") || newsVideo.includes("youtu.be")) {
              if (newsVideo.includes("youtube.com/watch")) {
                const videoId = new URL(newsVideo).searchParams.get("v");
                if (videoId) {
                  finalVideoSrc = `https://www.youtube.com/embed/${videoId}?rel=0&showinfo=0&autoplay=1`;
                }
              } else if (newsVideo.includes("youtu.be")) {
                const videoId = newsVideo.split("youtu.be/")[1].split("?")[0];
                if (videoId) {
                  finalVideoSrc = `https://www.youtube.com/embed/${videoId}?rel=0&showinfo=0&autoplay=1`;
                }
              }
            }

            // Create professional video container with loading indicator
            videoContainer.innerHTML = `
              <div class="video-container mt-4">
                <div class="video-loading-indicator">
                  <div class="spinner-border text-danger" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                  </div>
                  <p>جاري تحميل الفيديو...</p>
                </div>
                <iframe
                  src="${finalVideoSrc}"
                  width="100%"
                  height="400"
                  allowfullscreen
                  frameborder="0"
                  class="video-iframe"
                  onload="this.parentNode.classList.add('loaded')"
                ></iframe>
              </div>`;

            // Hide the button
            showVideoBtn.style.display = 'none';

            // Fallback in case onload doesn't trigger
            setTimeout(() => {
              const videoContainer = document.querySelector('.video-container');
              if (videoContainer && !videoContainer.classList.contains('loaded')) {
                videoContainer.classList.add('loaded');
              }
            }, 3000);
          };
        } else {
          // No video available, hide the button
          showVideoBtn.style.display = 'none';
        }
      }
    });

    // Enhanced modal close event handler
    newsDetailModal.addEventListener("hidden.bs.modal", function () {
      // Clear video with enhanced handling
      const videoContainer = newsDetailModal.querySelector("#newsDetailVideoContainer");
      if (videoContainer) {
        videoContainer.innerHTML = ""; // Clear video when modal is closed
      }

      // Reset video button with enhanced visibility handling
      const showVideoBtn = newsDetailModal.querySelector("#showVideoBtn");
      if (showVideoBtn) {
        // Reset button visibility based on whether there's a video
        const lastVideoUrl = showVideoBtn.getAttribute("data-last-video");
        if (lastVideoUrl && lastVideoUrl.trim() !== '') {
          showVideoBtn.style.display = 'inline-block';
        } else {
          showVideoBtn.style.display = 'none';
        }
      }

      // Exit fullscreen mode if active
      if (isFullscreen) {
        const galleryElement = newsDetailModal.querySelector('.news-gallery');
        if (galleryElement) {
          galleryElement.classList.remove('fullscreen');
          document.body.style.overflow = ''; // Restore scrolling
          isFullscreen = false;
        }
      }

      // Reset gallery state
      galleryImages = [];
      currentSlideIndex = 0;

      // Reset any loading indicators
      const loadingIndicators = newsDetailModal.querySelectorAll('.slide-loading-indicator');
      loadingIndicators.forEach(indicator => {
        if (indicator.parentNode) {
          indicator.parentNode.removeChild(indicator);
        }
      });

      // Remove any loaded classes
      const loadedElements = newsDetailModal.querySelectorAll('.loaded');
      loadedElements.forEach(element => {
        element.classList.remove('loaded');
      });
    });
  }

  // Enhanced Professional Video Modal
  const videoModal = document.getElementById("videoModal");
  if (videoModal) {
    videoModal.addEventListener("show.bs.modal", function (event) {
      const button = event.relatedTarget;
      const videoSrc = button.getAttribute("data-video-src");
      const newsTitle = button.getAttribute("data-news-title");
      const newsDesc = button.getAttribute("data-news-desc");
      const newsDate = button.getAttribute("data-news-date");
      const newsCategory = button.getAttribute("data-news-category") || 'فيديو';

      const modalTitle = videoModal.querySelector(".modal-title");
      const modalDesc = videoModal.querySelector(".video-modal-description");
      const modalDate = videoModal.querySelector(".date-value");
      const modalCategory = videoModal.querySelector(".category-value");
      const iframe = videoModal.querySelector(".video-iframe");
      const videoContainer = videoModal.querySelector(".professional-video-container");

      // Reset loading state
      if (videoContainer) {
        videoContainer.classList.remove('loaded');
      }

      // Set modal content with professional styling
      if (modalTitle) modalTitle.textContent = newsTitle || 'فيديو توضيحي';
      if (modalDesc) modalDesc.textContent = newsDesc || '';
      if (modalDate) modalDate.textContent = newsDate || '';
      if (modalCategory) modalCategory.textContent = newsCategory;

      // Process video URL with enhanced error handling
      if (videoSrc && videoSrc.trim() !== '') {
        let finalVideoSrc = videoSrc;

        // Check if video URL is from Google Drive
        if (videoSrc.includes("drive.google.com")) {
          let fileId = "";
          if (videoSrc.includes("/file/d/")) {
            fileId = videoSrc.split("/file/d/")[1].split("/")[0];
          } else if (videoSrc.includes("id=")) {
            fileId = videoSrc.split("id=")[1].split("&")[0];
          }

          if (fileId) {
            finalVideoSrc = `https://drive.google.com/file/d/${fileId}/preview`;
          }
        }
        // Check if video URL is from YouTube
        else if (videoSrc.includes("youtube.com") || videoSrc.includes("youtu.be")) {
          // Convert to embed URL if it's not already
          if (videoSrc.includes("youtube.com/watch")) {
            const videoId = new URL(videoSrc).searchParams.get("v");
            if (videoId) {
              finalVideoSrc = `https://www.youtube.com/embed/${videoId}?rel=0&showinfo=0&autoplay=1`;
            }
          } else if (videoSrc.includes("youtu.be")) {
            const videoId = videoSrc.split("youtu.be/")[1].split("?")[0];
            if (videoId) {
              finalVideoSrc = `https://www.youtube.com/embed/${videoId}?rel=0&showinfo=0&autoplay=1`;
            }
          }
        }

        // Set the iframe source with the processed URL
        if (iframe) {
          iframe.src = finalVideoSrc;

          // Add event listener for iframe load
          iframe.onload = function() {
            if (videoContainer) {
              videoContainer.classList.add('loaded');
            }
          };

          // Fallback in case onload doesn't trigger
          setTimeout(() => {
            if (videoContainer && !videoContainer.classList.contains('loaded')) {
              videoContainer.classList.add('loaded');
            }
          }, 3000);
        }
      } else {
        // No video URL provided
        if (modalDesc) {
          modalDesc.innerHTML += '<div class="alert alert-warning mt-3">عذراً، لا يوجد فيديو متاح لهذا المحتوى.</div>';
        }

        // Hide loading indicator
        if (videoContainer) {
          const loadingIndicator = videoContainer.querySelector('.video-loading-indicator');
          if (loadingIndicator) {
            loadingIndicator.innerHTML = '<div class="alert alert-warning">لا يوجد فيديو متاح</div>';
          }

          // Force loaded state after a delay
          setTimeout(() => {
            videoContainer.classList.add('loaded');
          }, 1000);
        }
      }
    });

    // Clear video when modal is closed
    videoModal.addEventListener("hidden.bs.modal", function () {
      const iframe = videoModal.querySelector(".video-iframe");
      if (iframe) {
        iframe.src = ""; // Stop the video
      }

      // Reset loading state for next time
      const videoContainer = videoModal.querySelector(".professional-video-container");
      if (videoContainer) {
        videoContainer.classList.remove('loaded');
      }
    });
  }
});
