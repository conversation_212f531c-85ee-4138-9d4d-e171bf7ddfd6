#!/bin/bash
# This is not meant to be executed directly, but rather to be followed step by step
# Each command is explained with comments

# Step 1: Update system packages
echo "Step 1: Update system packages"
# sudo apt update
# sudo apt upgrade -y

# Step 2: Install required packages
echo "Step 2: Install required packages"
# sudo apt install -y python3-pip python3-venv nginx

# Step 3: Set up project directory (assuming you've already transferred your files)
echo "Step 3: Set up project directory"
# cd /home/<USER>/KLC_django
# sudo chown -R ubuntu:ubuntu /home/<USER>/KLC_django

# Step 4: Set up Python virtual environment
echo "Step 4: Set up Python virtual environment"
# python3 -m venv venv
# source venv/bin/activate
# pip install -r requirements.txt
# pip install gunicorn

# Step 5: Update Django settings for production
echo "Step 5: Update Django settings for production"
# Edit KLC/KLC/settings.py to update:
# - DEBUG = False
# - ALLOWED_HOSTS = ['***********', 'localhost', '127.0.0.1']
# - SECURE_SSL_REDIRECT = True (for HTTPS)
# - SESSION_COOKIE_SECURE = True (for HTTPS)
# - CSRF_COOKIE_SECURE = True (for HTTPS)

# Step 6: Collect static files
echo "Step 6: Collect static files"
# python manage.py collectstatic --noinput

# Step 7: Create systemd service file
echo "Step 7: Create systemd service file"
# sudo nano /etc/systemd/system/klc-django.service
# Add the following content:
# [Unit]
# Description=KLC Django Application
# After=network.target
#
# [Service]
# User=ubuntu
# Group=ubuntu
# WorkingDirectory=/home/<USER>/KLC_django
# ExecStart=/home/<USER>/KLC_django/venv/bin/gunicorn --workers 3 --bind 127.0.0.1:8000 KLC.wsgi:application
# Restart=on-failure
#
# [Install]
# WantedBy=multi-user.target

# Step 8: Enable and start the service
echo "Step 8: Enable and start the service"
# sudo systemctl daemon-reload
# sudo systemctl enable klc-django.service
# sudo systemctl start klc-django.service

# Step 9: Check service status
echo "Step 9: Check service status"
# sudo systemctl status klc-django.service

# Step 10: Generate SSL certificates for HTTPS
echo "Step 10: Generate SSL certificates for HTTPS"
# sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
#     -keyout /etc/ssl/private/klc-selfsigned.key \
#     -out /etc/ssl/certs/klc-selfsigned.crt \
#     -subj "/CN=***********"

# Step 11: Create Nginx configuration
echo "Step 11: Create Nginx configuration"
# sudo nano /etc/nginx/sites-available/klc
# Add the following content for HTTPS:
# server {
#     listen 80;
#     server_name ***********;
#     return 301 https://$host$request_uri;
# }
#
# server {
#     listen 443 ssl;
#     server_name ***********;
#
#     ssl_certificate /etc/ssl/certs/klc-selfsigned.crt;
#     ssl_certificate_key /etc/ssl/private/klc-selfsigned.key;
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers HIGH:!aNULL:!MD5;
#     ssl_prefer_server_ciphers on;
#     ssl_session_cache shared:SSL:10m;
#
#     location /static/ {
#         alias /home/<USER>/KLC_django/staticfiles/;
#         expires 30d;
#         add_header Cache-Control "public, max-age=2592000";
#     }
#
#     location /media/ {
#         alias /home/<USER>/KLC_django/media/;
#         expires 30d;
#         add_header Cache-Control "public, max-age=2592000";
#     }
#
#     location / {
#         proxy_pass http://127.0.0.1:8000;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#     }
#
#     # Additional security headers
#     add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
#     add_header X-Content-Type-Options nosniff;
#     add_header X-Frame-Options DENY;
#     add_header X-XSS-Protection "1; mode=block";
# }

# Step 12: Enable the site and test configuration
echo "Step 12: Enable the site and test configuration"
# sudo ln -sf /etc/nginx/sites-available/klc /etc/nginx/sites-enabled/
# sudo rm /etc/nginx/sites-enabled/default  # Remove default site
# sudo nginx -t  # Test configuration

# Step 13: Configure firewall
echo "Step 13: Configure firewall"
# sudo ufw allow 80
# sudo ufw allow 443
# sudo ufw enable

# Step 14: Restart Nginx
echo "Step 14: Restart Nginx"
# sudo systemctl restart nginx

# Step 15: Test the deployment
echo "Step 15: Test the deployment"
# Visit https://*********** in your browser

echo "Setup complete! Your Django application should now be running with HTTPS on Nginx."
