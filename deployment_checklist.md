# Local Deployment Checklist

## 1. Environment Setup
- [ ] Create and activate virtual environment
  ```bash
  python -m venv venv
  source venv/bin/activate  # On Windows: venv\Scripts\activate
  ```
- [ ] Install dependencies
  ```bash
  pip install -r requirements.txt
  ```

## 2. Static Files
- [ ] Collect static files
  ```bash
  python manage.py collectstatic
  ```
- [ ] Verify static files in staticfiles directory
- [ ] Check if all static files are properly referenced in templates

## 3. Database
- [ ] Run migrations
  ```bash
  python manage.py migrate
  ```
- [ ] Create superuser (if needed)
  ```bash
  python manage.py createsuperuser
  ```

## 4. Security
- [ ] Update SECRET_KEY in .env file
- [ ] Set DEBUG=False in settings.py
- [ ] Configure ALLOWED_HOSTS
- [ ] Check security settings in settings.py

## 5. Firebase Configuration
- [ ] Verify firebase_key.json exists
- [ ] Check Firebase credentials
- [ ] Test Firebase connection

## 6. Testing
- [ ] Run tests
  ```bash
  python manage.py test
  ```
- [ ] Check all forms and views
- [ ] Verify admin interface
- [ ] Test file uploads
- [ ] Check error pages (404, 500)

## 7. Performance
- [ ] Enable caching
- [ ] Optimize static files
- [ ] Check database queries

## 8. Documentation
- [ ] Update README.md
- [ ] Document deployment process
- [ ] List all environment variables

## 9. Backup
- [ ] Backup database
- [ ] Backup static files
- [ ] Backup media files

## 10. Final Checks
- [ ] Check all URLs
- [ ] Verify forms submission
- [ ] Test file uploads
- [ ] Check email functionality
- [ ] Verify admin interface
- [ ] Test user authentication
- [ ] Check error handling 