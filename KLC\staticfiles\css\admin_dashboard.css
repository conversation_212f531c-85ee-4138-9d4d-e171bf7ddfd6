:root {
    --primary-color: #3498db;
    --secondary-color: #2c3e50;
    --success-color: #2ecc71;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --light-color: #ecf0f1;
}

body {
    font-family: 'Cairo', sans-serif;
    background-color: #f5f6fa;
}

.sidebar {
    background: var(--secondary-color);
    min-height: 100vh;
    box-shadow: 0 0 15px rgba(0,0,0,0.2);
    padding: 20px 0;
}

.sidebar .navbar-brand {
    color: var(--light-color);
    font-size: 1.5rem;
    padding: 10px 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.nav-link {
    color: var(--light-color) !important;
    padding: 12px 20px;
    margin: 5px 0;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.nav-link:hover, .nav-link.active {
    background: rgba(255,255,255,0.1);
    transform: translateX(-5px);
}

.nav-link i {
    margin-left: 10px;
    width: 20px;
    text-align: center;
}

.main-content {
    padding: 30px;
}

.section-header {
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 15px;
    border-bottom: 1px solid #e0e0e0;
}

.section-header h2 {
    color: var(--secondary-color);
    font-weight: 600;
    margin: 0;
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.05);
    margin-bottom: 30px;
    overflow: hidden;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;
    padding: 20px;
}

.table {
    margin-bottom: 0;
}

.table thead th {
    background-color: var(--secondary-color);
    color: #fff;
    font-weight: 500;
    padding: 15px;
    border: none;
    vertical-align: middle;
}

.table tbody td {
    padding: 15px;
    vertical-align: middle;
    border-color: #f0f0f0;
}

.table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

.action-buttons .btn {
    padding: 6px 10px;
    border-radius: 5px;
    margin: 0 2px;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

.search-form {
    background-color: #fff;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.05);
    margin-bottom: 30px;
}

.modal-content {
    border-radius: 10px;
    overflow: hidden;
}

.modal-header {
    padding: 15px 20px;
}

.pagination {
    justify-content: center;
    margin-top: 20px;
}

.pagination .page-link {
    padding: 8px 16px;
    margin: 0 5px;
    border-radius: 5px;
    color: var(--secondary-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-control:focus, .form-select:focus {
    box-shadow: 0 0 0 0.25rem rgba(52, 152, 219, 0.25);
    border-color: var(--primary-color);
}

/* Transaction Status Badges */
.badge {
    padding: 0.5em 1em;
    font-size: 0.85em;
    font-weight: 600;
    border-radius: 6px;
}

.badge.bg-success {
    background: linear-gradient(90deg, #22c55e 60%, #15803d 100%) !important;
    color: #fff;
}

.badge.bg-warning {
    background: linear-gradient(90deg, #f59e42 60%, #b45309 100%) !important;
    color: #fff;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.action-buttons .btn {
    padding: 0.4rem 0.6rem;
    font-size: 0.9rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.action-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.action-buttons .btn-success {
    background: linear-gradient(90deg, #22c55e 60%, #15803d 100%);
    border: none;
}

.action-buttons .btn-danger {
    background: linear-gradient(90deg, #ef4444 60%, #991b1b 100%);
    border: none;
}

/* Dark mode styles for badges and buttons */
.dark-mode .badge.bg-success {
    background: linear-gradient(90deg, #22c55e 60%, #15803d 100%) !important;
}

.dark-mode .badge.bg-warning {
    background: linear-gradient(90deg, #f59e42 60%, #b45309 100%) !important;
}

.dark-mode .action-buttons .btn-success {
    background: linear-gradient(90deg, #22c55e 60%, #15803d 100%);
}

.dark-mode .action-buttons .btn-danger {
    background: linear-gradient(90deg, #ef4444 60%, #991b1b 100%);
}

/* Mobile Responsive Styles */
@media (max-width: 767.98px) {
    .main-content {
        padding: 15px;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
        margin-bottom: 20px;
        padding-bottom: 10px;
    }

    .section-header h2 {
        font-size: 1.3rem;
    }

    .card {
        margin-bottom: 20px;
    }

    .card-header {
        padding: 15px;
    }

    .table thead th {
        padding: 10px;
        font-size: 0.9rem;
    }

    .table tbody td {
        padding: 10px;
        font-size: 0.9rem;
    }

    .action-buttons {
        flex-wrap: wrap;
        justify-content: center;
    }

    .action-buttons .btn {
        margin-bottom: 5px;
        padding: 0.35rem 0.5rem;
        font-size: 0.85rem;
    }

    .search-form {
        padding: 15px;
        margin-bottom: 20px;
    }

    .pagination .page-link {
        padding: 6px 12px;
        margin: 0 3px;
        font-size: 0.9rem;
    }

    .badge {
        padding: 0.4em 0.8em;
        font-size: 0.8em;
    }

    /* Improve DataTables on mobile */
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter {
        text-align: right;
        margin-bottom: 10px;
    }

    .dataTables_wrapper .dataTables_filter input {
        width: 100%;
        margin-right: 0;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.3em 0.6em;
        font-size: 0.9em;
    }

    /* Dashboard stats cards */
    .stat-card {
        margin-bottom: 15px;
    }

    .stat-value {
        font-size: 1.5rem;
    }

    /* Modal adjustments */
    .modal-header {
        padding: 12px 15px;
    }

    .modal-body {
        padding: 15px;
    }

    .modal-footer {
        padding: 12px 15px;
    }
}

/* Small mobile devices */
@media (max-width: 575.98px) {
    .section-header h2 {
        font-size: 1.2rem;
    }

    .table-responsive {
        border-radius: 8px;
        overflow: hidden;
    }

    .action-buttons {
        flex-direction: column;
        align-items: stretch;
    }

    .action-buttons .btn {
        margin-bottom: 5px;
        width: 100%;
    }

    .pagination .page-link {
        padding: 5px 10px;
        margin: 0 2px;
        font-size: 0.85rem;
    }
}

