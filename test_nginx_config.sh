#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Print section header
section() {
    echo -e "\n${GREEN}==== $1 ====${NC}\n"
}

# Print info message
info() {
    echo -e "${YELLOW}INFO: $1${NC}"
}

# Print error message
error() {
    echo -e "${RED}ERROR: $1${NC}"
}

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    error "Please run as root (use sudo)"
    exit 1
fi

section "Testing Nginx Configuration"

# Test Nginx configuration syntax
info "Testing Nginx configuration syntax..."
nginx -t

if [ $? -ne 0 ]; then
    error "Nginx configuration test failed. Please fix the errors and try again."
    exit 1
fi

# Restart Nginx to apply changes
section "Restarting Nginx"
info "Restarting Nginx to apply changes..."
systemctl restart nginx

if [ $? -ne 0 ]; then
    error "Failed to restart Nginx. Please check the service status."
    exit 1
fi

info "Nginx restarted successfully."

section "Testing Access Restrictions"

# Get the server's IP address
SERVER_IP=$(hostname -I | awk '{print $1}')
info "Server IP: $SERVER_IP"

# Test access to admin pages from local network
info "Testing access to admin pages from local network..."
info "This should be ALLOWED."
info "Try accessing https://$SERVER_IP/admin from a browser on your local network."

# Test access to admin pages from outside network
info "Testing access to admin pages from outside network..."
info "This should be DENIED with a 403 error."
info "Try accessing https://$SERVER_IP/admin from a device outside your local network."
info "You can simulate this by using a mobile data connection or a VPN."

section "Verification Steps"

echo "1. From a device on your local network (192.168.1.x):"
echo "   - Visit https://$SERVER_IP/admin"
echo "   - You should be able to access the admin login page"
echo ""
echo "2. From a device outside your local network:"
echo "   - Visit https://$SERVER_IP/admin"
echo "   - You should see the custom 403 error page"
echo ""
echo "3. From any device:"
echo "   - Visit https://$SERVER_IP/ (the main site)"
echo "   - You should be able to access the public pages"

section "Troubleshooting"

echo "If you encounter issues:"
echo ""
echo "1. Check Nginx error logs:"
echo "   sudo tail -f /var/log/nginx/error.log"
echo ""
echo "2. Check Nginx access logs:"
echo "   sudo tail -f /var/log/nginx/access.log"
echo ""
echo "3. Verify the custom 403 page exists at:"
echo "   /home/<USER>/KLC_django/KLC_App/templates/KLC_App/errors/403.html"
echo ""
echo "4. Make sure the directory structure for the 403 page is correct:"
echo "   sudo mkdir -p /home/<USER>/KLC_django/KLC_App/templates/KLC_App/errors"
echo ""
echo "5. Ensure proper permissions:"
echo "   sudo chown -R ubuntu:ubuntu /home/<USER>/KLC_django"
echo "   sudo chmod -R 755 /home/<USER>/KLC_django"

section "Done"
info "Testing script completed. Follow the verification steps above to confirm the configuration is working correctly."
