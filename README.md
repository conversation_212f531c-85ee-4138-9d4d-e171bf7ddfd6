# KLC Village Council Website

A Django-based web application for the KLC Village Council, providing electronic services and management tools.

## Features

- User authentication and authorization
- Electronic services management
- Hall reservations system
- Transaction management
- Suggestions and complaints system
- Admin dashboard
- Dark/Light mode support
- Responsive design

## Prerequisites

- Python 3.8 or higher
- Firebase account and credentials
- Virtual environment (recommended)

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd KLC_django
```

2. Create and activate virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Set up environment variables:
- Create a `.env` file in the project root
- Add the following variables:
  ```
  DEBUG=False
  SECRET_KEY=your-secret-key
  ALLOWED_HOSTS=localhost,127.0.0.1
  ```

5. Set up Firebase:
- Place your `firebase_key.json` in `KLC_App/settings/`

6. Run migrations:
```bash
python manage.py migrate
```

7. Collect static files:
```bash
python manage.py collectstatic
```

8. Create superuser (optional):
```bash
python manage.py createsuperuser
```

## Running the Application

1. Start the development server:
```bash
python manage.py runserver
```

2. Access the application:
- Main site: http://localhost:8000
- Admin interface: http://localhost:8000/admin

## Project Structure

```
KLC_django/
├── KLC/                  # Project settings
├── KLC_App/             # Main application
│   ├── static/          # Static files
│   ├── templates/       # HTML templates
│   └── views.py         # View functions
├── staticfiles/         # Collected static files
├── media/              # User-uploaded files
├── logs/               # Log files
├── requirements.txt    # Project dependencies
└── manage.py          # Django management script
```

## Security Considerations

- DEBUG mode is disabled in production
- SECRET_KEY is stored in environment variables
- CSRF protection is enabled
- XSS protection is enabled
- Secure headers are configured

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
