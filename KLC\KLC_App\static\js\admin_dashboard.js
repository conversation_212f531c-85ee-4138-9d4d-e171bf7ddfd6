const today = new Date();
const minSelectableDate = new Date();
minSelectableDate.setDate(today.getDate() + 4);
const endOfYear = new Date(today.getFullYear(), 11, 31);

flatpickr("#startDateInput", {
    dateFormat: "Y-m-d",
    minDate: minSelectableDate,
    maxDate: endOfYear,
    locale: "ar",
    onChange: function (selectedDates, dateStr, instance) {
        if (selectedDates.length > 0) {
            const startDate = selectedDates[0];
            const minEndDate = new Date(startDate);
            const maxEndDate = new Date(startDate);
            maxEndDate.setDate(startDate.getDate() + 7);

            if (maxEndDate > endOfYear) {
                maxEndDate.setTime(endOfYear.getTime());
            }

            endPicker.set('minDate', minEndDate);
            endPicker.set('maxDate', maxEndDate);
        }
    }
});

const endPicker = flatpickr("#endDateInput", {
    dateFormat: "Y-m-d",
    locale: "ar",
});


minSelectableDate.setDate(today.getDate() + 4);

const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
};

const startDateInput = document.getElementById('startDateInput');
const endDateInput = document.getElementById('endDateInput');

startDateInput.setAttribute('min', formatDate(minSelectableDate));
startDateInput.setAttribute('max', formatDate(endOfYear));

startDateInput.addEventListener('change', function () {
    const selectedStartDate = new Date(this.value);

    if (isNaN(selectedStartDate)) {
        endDateInput.value = '';
        endDateInput.setAttribute('min', '');
        endDateInput.setAttribute('max', '');
        return;
    }

    const minEndDate = new Date(selectedStartDate);

    const maxEndDate = new Date(selectedStartDate);
    maxEndDate.setDate(selectedStartDate.getDate() + 7);

    if (maxEndDate > endOfYear) {
        maxEndDate.setTime(endOfYear.getTime());
    }

    endDateInput.setAttribute('min', formatDate(minEndDate));
    endDateInput.setAttribute('max', formatDate(maxEndDate));

    // Reset end date if it's out of new range
    if (endDateInput.value) {
        const selectedEndDate = new Date(endDateInput.value);
        if (selectedEndDate < minEndDate || selectedEndDate > maxEndDate) {
            endDateInput.value = '';
        }
    }
});

window.addEventListener('load', () => {
    startDateInput.dispatchEvent(new Event('change'));
});

$(document).ready(function () {
    $('#usersTable').DataTable({
        language: {
            url: "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
        },
        responsive: true,
        dom: '<"row mb-3"<"col-sm-6"l><"col-sm-6"f>>' + // l = length menu, f = filter input
            '<"row"<"col-sm-12"tr>>' +
            '<"row mt-3"<"col-sm-5"i><"col-sm-7"p>>',
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]],
        buttons: [
            {
                extend: 'copyHtml5',
                text: '<i class="fas fa-copy"></i> نسخ',
                className: 'btn btn-secondary btn-sm'
            },
            {
                extend: 'excelHtml5',
                text: '<i class="fas fa-file-excel"></i> تصدير Excel',
                className: 'btn btn-success btn-sm'
            },
            {
                extend: 'print',
                text: '<i class="fas fa-print"></i> طباعة',
                className: 'btn btn-primary btn-sm'
            }
        ],
        initComplete: function () {
            $('#reservationsTable_wrapper .dt-buttons').addClass('mb-3');
        }
    });
});

$(document).ready(function () {
    $('#transactionsTable').DataTable({
        language: {
            url: "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
        },
        responsive: true,
        dom: '<"row mb-3"<"col-sm-6"l><"col-sm-6"f>>' + // l = length menu, f = filter input
            '<"row"<"col-sm-12"tr>>' +
            '<"row mt-3"<"col-sm-5"i><"col-sm-7"p>>',
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]],
        buttons: [
            {
                extend: 'copyHtml5',
                text: '<i class="fas fa-copy"></i> نسخ',
                className: 'btn btn-secondary btn-sm'
            },
            {
                extend: 'excelHtml5',
                text: '<i class="fas fa-file-excel"></i> تصدير Excel',
                className: 'btn btn-success btn-sm'
            },
            {
                extend: 'print',
                text: '<i class="fas fa-print"></i> طباعة',
                className: 'btn btn-primary btn-sm'
            }
        ],
        initComplete: function () {
            $('#reservationsTable_wrapper .dt-buttons').addClass('mb-3'); // تعديل الشكل للمزيد من الجمالية
        }
    });
});

$(document).ready(function () {
    $('#reservationsTable').DataTable({
        language: {
            url: "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
        },
        responsive: true,
        dom: '<"row mb-3"<"col-sm-6"l><"col-sm-6"f>>' + // l = length menu, f = filter input
            '<"row"<"col-sm-12"tr>>' +
            '<"row mt-3"<"col-sm-5"i><"col-sm-7"p>>',
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]],
        buttons: [
            {
                extend: 'copyHtml5',
                text: '<i class="fas fa-copy"></i> نسخ',
                className: 'btn btn-secondary btn-sm'
            },
            {
                extend: 'excelHtml5',
                text: '<i class="fas fa-file-excel"></i> تصدير Excel',
                className: 'btn btn-success btn-sm'
            },
            {
                extend: 'print',
                text: '<i class="fas fa-print"></i> طباعة',
                className: 'btn btn-primary btn-sm'
            }
        ],
        initComplete: function () {
            $('#reservationsTable_wrapper .dt-buttons').addClass('mb-3'); // تعديل الشكل للمزيد من الجمالية
        }
    });
});

document.addEventListener('DOMContentLoaded', function () {
    // The form submission handler for adminReservationForm has been moved to add_reservation.html
    // to prevent conflicts between multiple event handlers
});

document.addEventListener('DOMContentLoaded', function () {
    const settleDebtModal = document.getElementById('settleDebtModal');

    settleDebtModal.addEventListener('show.bs.modal', function (event) {
        const button = event.relatedTarget;
        const userId = button.getAttribute('data-user-id');
        const userName = button.getAttribute('data-user-name');
        const userDebt = button.getAttribute('data-user-debt');

        // Update modal content
        document.getElementById('userName').textContent = userName;
        document.getElementById('currentDebtAmount').textContent = userDebt;

        // Update form action
        const form = document.getElementById('settleDebtForm');
        form.action = `/pay_debt/${userId}/`;

        // Update input max value
        const amountInput = form.querySelector('input[name="amount"]');
        amountInput.setAttribute('max', userDebt);
        amountInput.placeholder = `أدخل مبلغ التسديد`;
    });
});

// Time limit for inactivity (5 minutes = 300000 milliseconds)
const INACTIVITY_TIMEOUT = 300000; // 5 minutes

let timeout;

// Function to log out the admin after inactivity
function logout() {
    // Redirect to your logout view or any URL that handles admin logout
    window.location.href = '/admin_logout'; // Adjust the URL as per your project setup
}

// Reset the inactivity timer
function resetTimer() {
    clearTimeout(timeout);
    timeout = setTimeout(logout, INACTIVITY_TIMEOUT);
}

// Event listeners for user activity
window.onload = resetTimer;  // Reset timer when the page is loaded
window.onmousemove = resetTimer;  // Reset timer on mouse movement
window.onkeydown = resetTimer;  // Reset timer on key press


