/* Admin Responsive Forms RTL-specific CSS
 * This file contains RTL-specific styling for all admin forms
 * with responsive design for both desktop and mobile devices
 */

/* RTL specific adjustments */
.section-icon-circle {
    margin-right: 0;
    margin-left: 1rem;
}

.form-label i {
    margin-left: 0.5rem;
    margin-right: 0;
}

.input-group > .form-control:not(:last-child),
.input-group > .form-select:not(:last-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

.input-group > .form-control:not(:first-child),
.input-group > .form-select:not(:first-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.input-group > .input-group-text:first-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.input-group > .input-group-text:last-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.btn i {
    margin-right: 0;
    margin-left: 0.5rem;
}

.invalid-feedback i,
.form-text i,
.alert i {
    margin-right: 0;
    margin-left: 0.5rem;
}

/* RTL calendar styling */
.flatpickr-calendar.arrowTop:before,
.flatpickr-calendar.arrowTop:after {
    right: 50%;
    left: auto;
    transform: translateX(50%);
}

.flatpickr-calendar.arrowBottom:before,
.flatpickr-calendar.arrowBottom:after {
    right: 50%;
    left: auto;
    transform: translateX(50%);
}

.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
    padding: 10px;
}

.flatpickr-months .flatpickr-prev-month {
    right: auto;
    left: 0;
}

.flatpickr-months .flatpickr-next-month {
    right: 0;
    left: auto;
}

/* Mobile specific RTL adjustments */
@media (max-width: 767.98px) {
    .section-header .btn i {
        margin-left: 0.5rem;
        margin-right: 0;
    }
    
    .alert .btn-close {
        margin-right: auto;
        margin-left: 0;
    }
}