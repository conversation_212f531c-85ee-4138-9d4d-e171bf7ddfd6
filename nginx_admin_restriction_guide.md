# Restricting Admin Access with Nginx

This guide explains how to configure Nginx to restrict access to your Django admin pages based on IP addresses.

## Overview

The configuration we've implemented:

1. Allows access to admin pages (`/admin/*` and `/admin_login`) only from your local network (***********/24)
2. Denies access to these pages from all other IP addresses
3. Shows a custom 403 error page when access is denied
4. Maintains normal access to all other pages of your website

## Files Created/Modified

1. **klc_nginx_***********.conf**: The Nginx configuration file with IP-based access restrictions
2. **KLC_App/templates/KLC_App/errors/403.html**: A custom error page shown when access is denied
3. **test_nginx_config.sh**: A script to help test the Nginx configuration

## How It Works

### IP-Based Access Control

Nginx uses `allow` and `deny` directives to control access based on IP addresses:

```nginx
# Allow access from local network (***********/24)
allow ***********/24;
# Deny access from all other IPs
deny all;
```

The `***********/24` notation represents all IP addresses from *********** to *************, which covers your entire local network.

### Location Blocks

We've created specific location blocks for admin-related URLs:

1. **Admin pages**: `location ~ ^/admin { ... }`
   - The `~` symbol indicates a regular expression match
   - `^/admin` matches any URL that starts with "/admin"

2. **Admin login page**: `location = /admin_login { ... }`
   - The `=` symbol indicates an exact match
   - This specifically targets the admin login page

### Custom Error Page

When access is denied, Nginx shows a custom 403 error page:

```nginx
error_page 403 /403.html;

location = /403.html {
    root /home/<USER>/KLC_django/KLC_App/templates/KLC_App/errors;
    internal;
}
```

The `internal` directive ensures this location can only be used for internal redirects, not direct access.

## Installation Steps

1. **Copy the Nginx configuration file to the server**:
   ```bash
   sudo cp klc_nginx_***********.conf /etc/nginx/sites-available/klc
   ```

2. **Create a symbolic link to enable the site**:
   ```bash
   sudo ln -sf /etc/nginx/sites-available/klc /etc/nginx/sites-enabled/
   ```

3. **Remove the default site if it exists**:
   ```bash
   sudo rm -f /etc/nginx/sites-enabled/default
   ```

4. **Create the directory for the custom error page**:
   ```bash
   sudo mkdir -p /home/<USER>/KLC_django/KLC_App/templates/KLC_App/errors
   ```

5. **Copy the custom error page**:
   ```bash
   sudo cp KLC_App/templates/KLC_App/errors/403.html /home/<USER>/KLC_django/KLC_App/templates/KLC_App/errors/
   ```

6. **Set proper permissions**:
   ```bash
   sudo chown -R ubuntu:ubuntu /home/<USER>/KLC_django
   sudo chmod -R 755 /home/<USER>/KLC_django
   ```

7. **Test the Nginx configuration**:
   ```bash
   sudo nginx -t
   ```

8. **Restart Nginx to apply changes**:
   ```bash
   sudo systemctl restart nginx
   ```

## Testing the Configuration

Run the testing script to verify the configuration:

```bash
sudo bash test_nginx_config.sh
```

### Manual Testing

1. **From a device on your local network (192.168.1.x)**:
   - Visit `https://***********/admin`
   - You should be able to access the admin login page

2. **From a device outside your local network**:
   - Visit `https://***********/admin`
   - You should see the custom 403 error page

3. **From any device**:
   - Visit `https://***********/` (the main site)
   - You should be able to access the public pages

## Troubleshooting

If you encounter issues:

1. **Check Nginx error logs**:
   ```bash
   sudo tail -f /var/log/nginx/error.log
   ```

2. **Check Nginx access logs**:
   ```bash
   sudo tail -f /var/log/nginx/access.log
   ```

3. **Verify the custom 403 page exists**:
   ```bash
   ls -la /home/<USER>/KLC_django/KLC_App/templates/KLC_App/errors/403.html
   ```

4. **Check Nginx service status**:
   ```bash
   sudo systemctl status nginx
   ```

5. **Restart Nginx**:
   ```bash
   sudo systemctl restart nginx
   ```

## Modifying the Configuration

### Allowing Additional IP Addresses

To allow access from specific IP addresses outside your local network:

```nginx
# Allow access from local network
allow ***********/24;
# Allow specific external IPs
allow ************;  # Example external IP
# Deny access from all other IPs
deny all;
```

### Allowing Additional IP Ranges

To allow access from another IP range:

```nginx
# Allow access from local network
allow ***********/24;
# Allow access from another network
allow 10.0.0.0/24;
# Deny access from all other IPs
deny all;
```

### Changing the Error Page

To use a different error page:

1. Create a new HTML file for the error page
2. Update the Nginx configuration:
   ```nginx
   error_page 403 /custom-403.html;
   
   location = /custom-403.html {
       root /path/to/your/error/pages;
       internal;
   }
   ```

## Security Considerations

1. **Keep your Nginx configuration secure**:
   - Regularly update Nginx to the latest version
   - Use strong SSL/TLS settings
   - Implement proper firewall rules

2. **Don't rely solely on IP restrictions**:
   - This is just one layer of security
   - Always use strong passwords for admin accounts
   - Consider implementing two-factor authentication

3. **Monitor access logs**:
   - Regularly check Nginx access logs for unauthorized access attempts
   - Consider setting up log monitoring and alerting

## Conclusion

This configuration provides an additional layer of security by restricting admin access to your local network. Remember that IP-based restrictions are just one part of a comprehensive security strategy and should be combined with other security measures.
