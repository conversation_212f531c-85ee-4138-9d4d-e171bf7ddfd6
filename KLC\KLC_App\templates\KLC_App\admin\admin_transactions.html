{% extends 'KLC_App/admin/admin_base.html' %}

{% block title %}إدارة الطلبات - لوحة التحكم{% endblock %}

{% block body_class %}admin-transactions{% endblock %}

{% block content %}
<div class="section-header">
    <h2><i class="fas fa-tasks me-2"></i> إدارة الطلبات</h2>
    <div>
        <a href="{% url 'transaction_statistics' %}" class="btn btn-info me-2">
            <i class="fas fa-chart-bar me-1"></i> سجل الإحصائيات
        </a>
        <a href="{% url 'add_transaction' %}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-2"></i> إضافة طلب جديد
        </a>
    </div>
</div>

<!-- Policy Note -->
<div class="alert alert-info mb-4">
    <div class="d-flex align-items-center">
        <div class="alert-icon me-3">
            <i class="fas fa-info-circle fa-2x"></i>
        </div>
        <div>
            <h5 class="alert-heading mb-1">سياسة عرض الطلبات</h5>
            <p class="mb-0">
                تعرض هذه الصفحة الطلبات قيد التنفيذ فقط. الطلبات المكتملة التي لها رقم إيصال يتم نقلها تلقائياً إلى
                <a href="{% url 'transaction_statistics' %}" class="alert-link">سجل الإحصائيات</a>
                {% if completed_count > 0 %}
                <span class="badge bg-primary ms-1">{{ completed_count }} طلب مكتمل</span>
                {% endif %}
            </p>
        </div>
    </div>
</div>

<!-- Transactions Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title">
            <i class="fas fa-list-alt me-2"></i> قائمة الطلبات قيد التنفيذ
            <span class="badge bg-warning ms-2">غير مكتملة</span>
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table align-middle" id="transactionsTable">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>رقم الهوية</th>
                        <th>رقم الهاتف</th>
                        <th>نوع الطلب</th>
                        <th>ملاحظات إضافية</th>
                        <th>تاريخ الطلب</th>
                        <th>حالة الطلب</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for transaction in transactions %}
                    <tr {% if transaction.transaction_status == 'Done' and transaction.receipt_number %}class="locked-transaction"{% endif %}>
                        <td>{{ transaction.full_name }}</td>
                        <td>{{ transaction.id_number }}</td>
                        <td>{{ transaction.phone_number }}</td>
                        <td>{{ transaction.transaction_type }}</td>
                        <td>{{ transaction.additional_notes }}</td>
                        <td>{{ transaction.created_at }}</td>
                        <td>
                            {% if transaction.transaction_status == 'Done' %}
                                <span class="badge bg-success">مكتمل</span>
                            {% else %}
                                <span class="badge bg-warning">غير مكتمل</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="action-buttons">
                                {% if transaction.transaction_status != 'Done' %}
                                <button type="button" class="btn btn-success btn-sm show-receipt-modal"
                                        title="تحديد كمنجز"
                                        data-transaction-id="{{ transaction.id }}">
                                    <i class="fas fa-check"></i>
                                </button>
                                {% endif %}

                                {% if transaction.transaction_status == 'Done' and transaction.receipt_number %}
                                <!-- Disabled delete button with tooltip for completed transactions with receipt numbers -->
                                <button type="button" class="btn btn-secondary btn-sm" title="لا يمكن حذف الطلب المكتمل الذي له رقم إيصال" disabled>
                                    <i class="fas fa-lock"></i>
                                </button>
                                {% else %}
                                <!-- Delete button for other transactions -->
                                <form method="POST" action="{% url 'delete_transaction_admin' transaction.id %}" style="display: inline;">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-danger btn-sm" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذا الطلب؟')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="10" class="text-center py-4">
                            <div class="empty-state">
                                <i class="fas fa-clipboard-check empty-icon"></i>
                                <h6 class="mt-3">لا يوجد طلبات قيد التنفيذ</h6>
                                <p class="text-muted">جميع الطلبات مكتملة ومتاحة في <a href="{% url 'transaction_statistics' %}">سجل الإحصائيات</a></p>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>



<!-- Include the receipt modal -->
{% include 'KLC_App/admin/receipt_modal.html' %}
{% endblock %}

{% block extra_css %}
<style>
    /* Styling for locked transactions */
    tr.locked-transaction {
        background-color: rgba(0, 123, 255, 0.05) !important;
    }

    [data-theme="dark"] tr.locked-transaction {
        background-color: rgba(0, 123, 255, 0.1) !important;
    }

    .receipt-badge {
        font-weight: 600;
        font-size: 0.85rem;
        padding: 0.35rem 0.65rem;
    }

    .alert-icon {
        color: #0dcaf0;
        background-color: rgba(13, 202, 240, 0.1);
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    [data-theme="dark"] .alert-icon {
        background-color: rgba(13, 202, 240, 0.2);
    }

    /* Action buttons styling */
    .action-buttons {
        display: flex;
        gap: 5px;
        justify-content: center;
    }

    .action-buttons .btn {
        width: 36px;
        height: 36px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .action-buttons .btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    [data-theme="dark"] .action-buttons .btn:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    /* Empty state styling */
    .empty-state {
        padding: 2rem 1rem;
        text-align: center;
    }

    .empty-icon {
        font-size: 3rem;
        color: #6c757d;
        opacity: 0.5;
        margin-bottom: 1rem;
    }

    [data-theme="dark"] .empty-icon {
        color: #adb5bd;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function () {
        $('#transactionsTable').DataTable({
            language: {
                url: "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
            },
            responsive: true,
            dom: '<"dt-controls"<"row"<"col-md-6"l><"col-md-6"f>>>' +
                 '<"row"<"col-sm-12"tr>>' +
                 '<"row mt-3"<"col-sm-5"i><"col-sm-7"p>>',
            pageLength: 10,
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]],
            buttons: [
                {
                    extend: 'copyHtml5',
                    text: '<i class="fas fa-copy"></i> نسخ',
                    className: 'btn btn-secondary btn-sm'
                },
                {
                    extend: 'excelHtml5',
                    text: '<i class="fas fa-file-excel"></i> تصدير Excel',
                    className: 'btn btn-success btn-sm'
                },
                {
                    extend: 'print',
                    text: '<i class="fas fa-print"></i> طباعة',
                    className: 'btn btn-primary btn-sm'
                }
            ],
            initComplete: function () {
                $('#transactionsTable_wrapper .dt-buttons').addClass('mb-3');

                // Add placeholder to search input
                $('.dataTables_filter input').attr('placeholder', 'البحث...');
            }
        });

        // Handle receipt modal
        $('.show-receipt-modal').click(function() {
            const transactionId = $(this).data('transaction-id');
            $('#transaction_id').val(transactionId);
            $('#receiptModal').modal('show');
        });

        // Handle receipt form submission
        $('#receiptForm').submit(function(e) {
            e.preventDefault();
            const transactionId = $('#transaction_id').val();
            const receiptNumber = $('#receipt_number').val();

            if (!receiptNumber) {
                alert('الرجاء إدخال رقم الإيصال');
                return;
            }

            // Submit the form to the server
            $.ajax({
                url: "{% url 'update_transaction_status' 0 %}".replace('0', transactionId),
                type: 'POST',
                data: {
                    'receipt_number': receiptNumber,
                    'csrfmiddlewaretoken': $('input[name=csrfmiddlewaretoken]').val()
                },
                success: function(response) {
                    if (response.status === 'success') {
                        // Close the modal and reload the page
                        $('#receiptModal').modal('hide');
                        location.reload();
                    } else {
                        alert(response.message || 'حدث خطأ أثناء تحديث حالة الطلب');
                    }
                },
                error: function() {
                    alert('حدث خطأ أثناء الاتصال بالخادم');
                }
            });
        });
    });
</script>
{% endblock %}
