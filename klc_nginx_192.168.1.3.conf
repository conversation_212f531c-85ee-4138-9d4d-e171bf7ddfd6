server {
    listen 80;
    server_name ***********;

    # Redirect HTTP to HTTPS (uncomment when you have SSL set up)
    # return 301 https://$host$request_uri;

    # For HTTP-only setup (comment out if using HTTPS redirect above)
    location /static/ {
        alias /home/<USER>/KLC_django/KLC_App/static/;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
    }

    location /media/ {
        alias /home/<USER>/KLC_django/KLC_App/media/;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
    }

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Additional security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
}

server {
    listen 443 ssl;
    server_name ***********;

    # SSL certificates
    ssl_certificate /etc/ssl/certs/klc-selfsigned.crt;
    ssl_certificate_key /etc/ssl/private/klc-selfsigned.key;

    # SSL settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Static files
    location /static/ {
        alias /home/<USER>/KLC_django/KLC_App/static/;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
    }

    # Media files
    location /media/ {
        alias /home/<USER>/KLC_django/KLC_App/media/;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
    }

    # Admin pages - restrict access to local network only
    location ~ ^/admin {
        # Allow access from local network (***********/24)
        allow ***********/24;
        # Deny access from all other IPs
        deny all;

        # Return a custom 403 page for unauthorized access
        error_page 403 /403.html;

        # Pass to Django if access is allowed
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Admin login page - also restrict to local network
    location = /admin_login {
        # Allow access from local network (***********/24)
        allow ***********/24;
        # Deny access from all other IPs
        deny all;

        # Return a custom 403 page for unauthorized access
        error_page 403 /403.html;

        # Pass to Django if access is allowed
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # All other locations
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Custom 403 error page
    location = /403.html {
        root /home/<USER>/KLC_django/KLC_App/templates/KLC_App/errors;
        internal;
    }

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
}
