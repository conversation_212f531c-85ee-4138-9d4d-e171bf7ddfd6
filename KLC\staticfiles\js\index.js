/// For Bootstrap scrollspy and enhanced navigation
window.addEventListener("DOMContentLoaded", (event) => {
  // Activate Bootstrap scrollspy on the main nav element
  const mainNav = document.body.querySelector("#mainNav");
  if (mainNav) {
    new bootstrap.ScrollSpy(document.body, {
      target: "#mainNav",
      rootMargin: "0px 0px -40%",
    });

    // Add shadow to navbar on scroll
    window.addEventListener('scroll', function() {
      if (window.scrollY > 50) {
        mainNav.classList.add('navbar-scrolled', 'shadow');
      } else {
        mainNav.classList.remove('navbar-scrolled', 'shadow');
      }
    });
  }

  // Enhance navigation links with active state
  const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
  const sections = document.querySelectorAll('section, header');

  function setActiveNavLink() {
    let current = '';

    sections.forEach(section => {
      const sectionTop = section.offsetTop;
      const sectionHeight = section.clientHeight;

      if (window.scrollY >= (sectionTop - 200)) {
        current = section.getAttribute('id') || '';
      }
    });

    navLinks.forEach(link => {
      link.classList.remove('active');
      const href = link.getAttribute('href');
      if (href && href !== '#' && href.includes(current)) {
        link.classList.add('active');
      }
    });
  }

  // Set active link on page load and scroll
  setActiveNavLink();
  window.addEventListener('scroll', setActiveNavLink);

  // Collapse responsive navbar when toggler is visible
  const navbarToggler = document.body.querySelector(".navbar-toggler");
  const responsiveNavItems = [].slice.call(
    document.querySelectorAll("#navbarResponsive .nav-link")
  );
  responsiveNavItems.map(function (responsiveNavItem) {
    responsiveNavItem.addEventListener("click", () => {
      if (window.getComputedStyle(navbarToggler).display !== "none") {
        navbarToggler.click();
      }
    });
  });
});
// When the DOM is fully loaded, execute the following code
document.addEventListener("DOMContentLoaded", function () {
  // Form element
  // Get the start button (submit id button)
  const idInput = document.querySelector("#person-id");
  // Get the start button element
  const startButton = document.querySelector("#start-button");
  // If the user click enter button, it will submit the form
  handleEnterKeyPress();
  disableBackButton(); // Call the function to disable the back button
  // Add header background image and user messages
  addBackgroundImage("/static/images/header-background.jpg"); // Call the function to add the background image
  displayUserMessages(); // Call the function to display user messages
  // Call form functions
  preventImproperIdData(); // Call the function to prevent improper data entry
  /**
   * Function to handle the Enter key press event
   * This function allows the user to submit the form by pressing the Enter key
   * @param {Event} event - The keypress event
   * @returns {void}
   * @example
   // Example usage: handleEnterKeyPress(event);
    * @description This function listens for the Enter key press event on the ID input field and triggers a click on the start button to submit the form.
    * It prevents the default form submission behavior to ensure that the click event is triggered correctly.
   */
  function handleEnterKeyPress() {
    idInput.addEventListener("keypress", function (event) {
      if (event.key === "Enter") {
        event.preventDefault(); // Prevent the default form submission
        startButton.click();
      }
    });
  }

  /**
   * Function to disable the back button in the browser
   * This function prevents the user from navigating back to the previous page
   */
  function disableBackButton() {
    // Push a dummy state to history
    window.history.pushState(null, null, window.location.href);

    // Handle the back button by immediately pushing forward
    window.onpopstate = function () {
      window.history.go(1); // Prevent back navigation
    };
  }

  /**
   * Function to add a background image to the header section
   */
  function addBackgroundImage(path) {
    // Header elements
    const headerSection = document.querySelector("header");
    if (headerSection) {
      const backgroundDiv = document.createElement("div");
      backgroundDiv.style.backgroundImage = `url(${path})`;
      backgroundDiv.style.backgroundSize = "cover";
      backgroundDiv.style.backgroundPosition = "center";
      backgroundDiv.style.backgroundRepeat = "no-repeat";
      backgroundDiv.style.filter = "blur(2px)";
      backgroundDiv.style.position = "absolute";
      backgroundDiv.style.top = "0";
      backgroundDiv.style.left = "0";
      backgroundDiv.style.width = "100%";
      backgroundDiv.style.height = "100%";
      backgroundDiv.style.zIndex = "-1";
      headerSection.style.position = "relative";
      headerSection.appendChild(backgroundDiv);
      headerSection.style.overflow = "hidden"; // Prevent scrolling
      headerSection.style.height = "100vh"; // Set the height to 100% of the viewport height
    }
  }
  /**
   * Function to display user messages in the header section
   */
  function displayUserMessages() {
    const userMessageElement = document.querySelector("#header-text");

    const messages = [
      "مرحباً بك في موقع مجلس قروي كفرعين الإلكتروني.",
      "إستمتع بخدمات إلكترونية مميزة وسهلة الإستخدام.",
      "إكتشف خدماتنا المتنوعة التي تلبي احتياجاتك.",
      "أدخل رقم هويتك الآن للإستفادة من خدمات المجلس.",
      "تمتَّع بطلب معاملات إلكترونية.",
      "إحجز قاعة المجلس الآن بسهولة عبر منصتنا الإلكترونية.",
      "تحقق من مستحقات النفايات بكل سهولة ويسر.",
      "تقدَّم بطلب شكوى أو إقتراح من خلال صندوق الشكاوى والإقتراحات.",
      "تعرَّف على آخر الأخبار والمشاريع في كفرعين.",
      "تواصل معنا لأي مشكلة تواجهك.",
      "مجلس قروي كفرعين - شريكك في التنمية والخدمات.",
    ];

    let current = 0;

    const tipElement = document.createElement("div");
    tipElement.style.color = "white";
    tipElement.style.fontWeight = "bold";
    tipElement.style.fontSize = "1.5rem";
    tipElement.style.textAlign = "center";
    tipElement.style.marginTop = "15px";
    tipElement.style.transition =
      "opacity 0.7s ease-in-out, transform 0.7s ease-in-out";
    tipElement.style.opacity = "0";
    tipElement.style.padding = "10px 20px";
    tipElement.style.borderRadius = "12px";
    tipElement.style.background = "rgb(220 53 69 / 80%)";
    tipElement.style.boxShadow = "0px 4px 10px rgba(0, 0, 0, 0.3)";
    tipElement.style.display = "inline-block";
    tipElement.style.maxWidth = "100%";

    userMessageElement.appendChild(tipElement);

    function updateMessage() {
      tipElement.style.opacity = "0";
      tipElement.style.transform = "translateY(10px)";

      setTimeout(() => {
        tipElement.textContent = messages[current];
        tipElement.style.opacity = "1";
        tipElement.style.transform = "translateY(0)";
        current = (current + 1) % messages.length;
      }, 500); // Wait for fade out before updating message
    }

    updateMessage(); // Show first message immediately
    setInterval(updateMessage, 6000); // Change every 6 seconds
  }

  /**
   * Function to prevent improper id data entry in the ID input field
   */
  function preventImproperIdData() {
    idInput.addEventListener("keypress", function (event) {
      // Allow only numbers and backspace
      if (!/^[0-9\b]+$/.test(event.key)) {
        event.preventDefault();
      }
    });
  }
  setupVideoModal();
  addCardHoverEffect();
  /**
   * Function to set up the video modal
   * This function initializes the video modal and sets up event listeners for showing and hiding the modal
   */
  function setupVideoModal() {
    const videoModal = document.getElementById("videoModal");
    if (!videoModal) return;

    const iframe = videoModal.querySelector("iframe");
    const modalTitle = videoModal.querySelector(".modal-title");
    const modalDescription = videoModal.querySelector(".modal-description");
    const modalDate = videoModal.querySelector(".modal-date");

    videoModal.addEventListener("show.bs.modal", function (event) {
      const button = event.relatedTarget;
      let videoSrc = button.getAttribute("data-video-src");

      // Ensure the URL has proper parameters for controls
      if (
        videoSrc.includes("youtube.com") ||
        videoSrc.includes("youtu.be") ||
        videoSrc.includes("drive.google.com")
      ) {
        // Add parameters to show controls (if not already present)
        if (!videoSrc.includes("?")) {
          videoSrc += "?";
        } else {
          videoSrc += "&";
        }
        videoSrc += "rel=0&controls=1&showinfo=1";
      }

      iframe.src = videoSrc;

      // Update modal content
      if (modalTitle)
        modalTitle.textContent =
          button.getAttribute("data-news-title") || "فيديو توضيحي";
      if (modalDescription)
        modalDescription.textContent =
          button.getAttribute("data-news-desc") || "";
      if (modalDate)
        modalDate.textContent = button.getAttribute("data-news-date") || "";
    });

    videoModal.addEventListener("hide.bs.modal", function () {
      iframe.src = ""; // Stop the video
    });
  }
  /**
   * Function to add hover effect to cards
   * This function adds a shadow and scaling effect to cards on mouseover and mouseout events
   */
  function addCardHoverEffect() {
    const cards = document.querySelectorAll(".news-card");
    cards.forEach((card) => {
      card.addEventListener("mouseover", () => {
        card.classList.add("news-card-hover");
      });
      card.addEventListener("mouseout", () => {
        card.classList.remove("news-card-hover");
      });
    });
  }
});
