from datetime import datetime
import pandas as pd
from django.conf import settings
import os
import uuid
import mimetypes
from io import BytesIO
# Get Firebase Firestore instance
db = settings.DB
# Get Firebase Storage instance
storage_bucket = settings.STORAGE_BUCKET

# Column mappings from Arabic to English
## Note that مستحقات has a white space at the beginning
PERSON_COLUMN_MAPPING = {
    "الاسم": "name",
    "مستحقات ": "debts_amount_2024",
    "رقم الهوية": "national_id"
}

### Excel file path from the admin (The cleaned file)
FILE_PATH = "../persons_data.xlsx"

## To import the new data to the database
def import_excel_to_db():
    try:
        # Load the Excel file
        df = pd.read_excel(FILE_PATH, engine="openpyxl", dtype=str)

        # Ensure required columns exist
        missing_cols = [col for col in PERSON_COLUMN_MAPPING if col not in df.columns]
        if missing_cols:
            print(f"Error: Missing columns in the Excel file: {missing_cols}")
            return

        # Rename columns
        df.rename(columns=PERSON_COLUMN_MAPPING, inplace=True)

        # Keep only relevant columns and remove rows with missing `national_id`
        df = df[list(PERSON_COLUMN_MAPPING.values())].dropna(subset=["national_id"])

        # Iterate through each row and add data to Firebase
        for _, row in df.iterrows():
            person_data = {
                "name": row["name"],
                "debts_amount_2024": row["debts_amount_2024"],
                "national_id": row["national_id"]
            }

            # Create a new document with national_id as ID
            db.collection("persons").document(person_data["national_id"]).set(person_data)

        print("Data successfully imported to Firebase!")

    except FileNotFoundError:
        print("Error: The specified file was not found.")
    except Exception as e:
        print(f"Unexpected error: {e}")

def import_excel_to_db(file_path):
    try:
        # Load the Excel file
        df = pd.read_excel(file_path, engine="openpyxl")

        # Ensure required columns exist
        missing_cols = [col for col in PERSON_COLUMN_MAPPING if col not in df.columns]
        if missing_cols:
            print(f"❌ Error: Missing columns in the Excel file: {missing_cols}")
            return

        # Rename columns
        df.rename(columns=PERSON_COLUMN_MAPPING, inplace=True)

        # Keep only relevant columns and remove rows with missing `national_id`
        df = df[list(PERSON_COLUMN_MAPPING.values())].dropna(subset=["national_id"])

        # Iterate through each row and add data to Firebase
        for _, row in df.iterrows():
            person_data = {
                "national_id": str(row["national_id"]),  # Ensure it's a string
                "name": str(row["name"]),
                "nameAsPerID": str(row["nameAsPerID"]),
            }

            # Create a new document with national_id as ID
            db.collection("persons").document(person_data["national_id"]).set(person_data)

        print("✅ Data successfully imported to Firebase!")

    except FileNotFoundError:
        print("❌ Error: The specified file was not found.")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def check_date_slot(start_date, end_date):
    # Ensure input dates are datetime objects
    if isinstance(start_date, str):
        start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
    if isinstance(end_date, str):
        end_date = datetime.strptime(end_date, "%Y-%m-%d").date()

    reservations_ref = db.collection("hall_reservations").stream()
    reservations = [reservation.to_dict() for reservation in reservations_ref]

    for reservation in reservations:
        res_start = datetime.strptime(reservation["start_date"], "%Y-%m-%d").date()
        res_end = datetime.strptime(reservation["end_date"], "%Y-%m-%d").date()

        # Check for any overlap
        if start_date <= res_end and end_date >= res_start:
            return False  # Conflict found

    return True  # No conflict

def upload_to_firebase_storage(file, folder='news_images', filename=None):
    """
    Upload a file to Firebase Storage first, then fall back to local storage if Firebase fails.
    If both fail, returns an error.

    Args:
        file: The file object to upload
        folder: The folder in Firebase Storage to upload to
        filename: Optional custom filename, if None a UUID will be generated

    Returns:
        A dictionary containing Firebase URL, local URL, and error status
    """
    # Generate a unique filename if not provided
    if filename is None:
        # Get the file extension
        file_extension = os.path.splitext(file.name)[1]
        # Generate a unique filename with UUID
        filename = f"{uuid.uuid4()}{file_extension}"
    else:
        # Add file extension if not provided in the filename
        if not os.path.splitext(filename)[1]:
            file_extension = os.path.splitext(file.name)[1]
            filename = f"{filename}{file_extension}"

    # Initialize result dictionary
    result = {
        'firebase_url': None,
        'local_url': None,
        'error': None
    }

    # Create copies of the file for both storage methods
    file_copy_firebase = BytesIO(file.read())
    file_copy_firebase.name = file.name

    # Reset the file pointer to the beginning
    file.seek(0)

    file_copy_local = BytesIO(file.read())
    file_copy_local.name = file.name

    # Reset the original file pointer to the beginning
    file.seek(0)

    # Try Firebase Storage first
    firebase_success = False
    if storage_bucket is not None:
        try:
            # Create the full path in Firebase Storage
            file_path = f"{folder}/{filename}"
            print(f"Uploading file to Firebase Storage: {file_path}")

            # Create a blob in the bucket
            blob = storage_bucket.blob(file_path)

            # Set the content type
            content_type, _ = mimetypes.guess_type(filename)
            if content_type:
                blob.content_type = content_type
            else:
                # Default to a common content type if we can't determine it
                blob.content_type = 'application/octet-stream'

            # Upload the file to Firebase
            blob.upload_from_file(file_copy_firebase)

            # Make the blob publicly accessible
            blob.make_public()

            # Get and print the public URL
            firebase_url = blob.public_url
            print(f"File uploaded successfully to Firebase. Public URL: {firebase_url}")

            # Add Firebase URL to result
            result['firebase_url'] = firebase_url
            firebase_success = True
        except Exception as e:
            import traceback
            print(f"Error uploading to Firebase Storage: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            print("Falling back to local storage.")

    # Try local storage if Firebase failed or is not available
    local_success = False
    if not firebase_success:
        try:
            local_url = upload_to_local_storage(file_copy_local, folder, filename)
            if local_url:
                result['local_url'] = local_url
                local_success = True
                print("Successfully uploaded to local storage as fallback.")
            else:
                print("Failed to upload to local storage.")
        except Exception as e:
            import traceback
            print(f"Error uploading to local storage: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
    else:
        # If Firebase succeeded, still try local storage as a backup
        try:
            local_url = upload_to_local_storage(file_copy_local, folder, filename)
            if local_url:
                result['local_url'] = local_url
                print("Successfully uploaded to both Firebase and local storage.")
        except Exception as e:
            print(f"Firebase upload succeeded, but local storage failed: {str(e)}")
            # This is not a critical error since Firebase succeeded

    # Check if at least one storage method succeeded
    if not firebase_success and not local_success:
        result['error'] = "Failed to upload image to both Firebase and local storage"
        print("ERROR: Failed to upload image to both Firebase and local storage")

    return result

def upload_to_local_storage(file, folder='news_images', filename=None):
    """
    Upload a file to local storage as a fallback

    Args:
        file: The file object to upload
        folder: The folder in local storage to upload to
        filename: Optional custom filename, if None a UUID will be generated

    Returns:
        The URL of the uploaded file
    """
    try:
        from django.core.files.base import ContentFile
        from django.conf import settings

        # Generate a unique filename if not provided
        if filename is None:
            # Get the file extension
            file_extension = os.path.splitext(file.name)[1]
            # Generate a unique filename with UUID
            filename = f"{uuid.uuid4()}{file_extension}"
        else:
            # Add file extension if not provided in the filename
            if not os.path.splitext(filename)[1]:
                file_extension = os.path.splitext(file.name)[1]
                filename = f"{filename}{file_extension}"

        # Reset file pointer to the beginning
        file.seek(0)

        # Save the file to the media directory
        from django.core.files.storage import default_storage
        image_path = default_storage.save(f"{folder}/{filename}", ContentFile(file.read()))

        # Use absolute URL path that works in both DEBUG and non-DEBUG modes
        image_url = f"{settings.MEDIA_URL}{image_path}"

        print(f"File uploaded successfully to local storage. URL: {image_url}")

        return image_url

    except Exception as e:
        import traceback
        print(f"Error uploading to local storage: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        return None

def delete_from_firebase_storage(file_url_or_dict):
    """
    Delete a file from Firebase Storage and/or local storage

    Args:
        file_url_or_dict: Either a URL string or a dictionary containing 'firebase_url' and 'local_url'

    Returns:
        True if at least one deletion was successful, False otherwise
    """
    firebase_success = False
    local_success = False

    # Handle dictionary format (new format)
    if isinstance(file_url_or_dict, dict):
        firebase_url = file_url_or_dict.get('firebase_url')
        local_url = file_url_or_dict.get('local_url')

        # Delete from Firebase if URL exists
        if firebase_url and 'storage.googleapis.com' in firebase_url:
            firebase_success = delete_firebase_file(firebase_url)

        # Delete from local storage if URL exists
        if local_url and (local_url.startswith('/media/') or local_url.startswith(settings.MEDIA_URL)):
            local_success = delete_from_local_storage(local_url)

        return firebase_success or local_success

    # Handle string format (legacy format)
    file_url = file_url_or_dict

    # Check if it's a Firebase Storage URL
    if 'storage.googleapis.com' in file_url:
        firebase_success = delete_firebase_file(file_url)
        return firebase_success

    # If it's a local storage URL
    elif '/media/' in file_url or settings.MEDIA_URL in file_url:
        local_success = delete_from_local_storage(file_url)
        return local_success

    return False

def delete_firebase_file(firebase_url):
    """
    Delete a file from Firebase Storage

    Args:
        firebase_url: The Firebase Storage URL

    Returns:
        True if deletion was successful, False otherwise
    """
    # Check if Firebase Storage is available
    if storage_bucket is None:
        print("Firebase Storage is not available. Cannot delete file from Firebase Storage.")
        return False

    try:
        # Extract the path from the URL
        # The URL format is typically: https://storage.googleapis.com/BUCKET_NAME/PATH
        # Split the URL to get the path
        path = firebase_url.split('storage.googleapis.com/')[1]
        # Remove the bucket name from the path
        path = path.split('/', 1)[1]

        print(f"Deleting file from Firebase Storage: {path}")

        # Create a blob reference
        blob = storage_bucket.blob(path)

        # Delete the blob
        blob.delete()

        print(f"File deleted successfully from Firebase Storage: {path}")
        return True

    except Exception as e:
        import traceback
        print(f"Error deleting from Firebase Storage: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        return False

def delete_from_local_storage(file_url):
    """
    Delete a file from local storage

    Args:
        file_url: The URL of the file to delete

    Returns:
        True if deletion was successful, False otherwise
    """
    try:
        from django.core.files.storage import default_storage
        from django.conf import settings

        # Extract the path after MEDIA_URL
        if file_url.startswith(settings.MEDIA_URL):
            image_path = file_url[len(settings.MEDIA_URL):]
        else:
            # Handle legacy paths that might start with /media/
            image_path = file_url.replace('/media/', '')

        print(f"Deleting file from local storage: {image_path}")

        # Delete the file
        default_storage.delete(image_path)

        print(f"File deleted successfully from local storage: {image_path}")
        return True

    except Exception as e:
        import traceback
        print(f"Error deleting from local storage: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        return False
