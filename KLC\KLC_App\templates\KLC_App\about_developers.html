{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>فريق المطورين</title>
    <!-- Favicon-->
    <link
      rel="icon"
      type="image/x-icon"
      href="{% static 'images/logo.png' %}"
    />
    <!-- Bootstrap 5.3 CSS RTL -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css"
    />
    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <!-- Google Fonts - Cairo -->
    <link
      href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap"
      rel="stylesheet"
    />
    <style>
      body {
        background-color: #f8f9fa;
        font-family: "Cairo", sans-serif;
        min-height: 100vh;
      }
      .bg-light {
        position: relative;
        background-color: #f8f9fa;
      }
      .bg-light::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url("{% static 'images/header-background.jpg' %}");
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        filter: blur(5px) brightness(0.7);
        z-index: -1;
      }
      .developer-card {
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        border: none;
        position: relative;
      }
      .developer-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
      }
      .developer-card::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          to bottom,
          rgba(0, 0, 0, 0.1) 0%,
          rgba(0, 0, 0, 0) 100%
        );
        pointer-events: none;
      }
      .profile-img {
        width: 200px;
        height: 200px;
        object-fit: cover;
        border: 5px solid white;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        transition: all 0.3s ease;
      }
      .developer-card:hover .profile-img {
        transform: scale(1.05);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
      }
      .social-icon {
        width: 42px;
        height: 42px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        margin: 0 6px;
        transition: all 0.3s ease;
        transform: translateY(0);
      }
      .social-icon:hover {
        transform: translateY(-5px) scale(1.1);
      }
      .bg-gradient-primary {
        background: linear-gradient(135deg, #990300 0%, #6700cd 100%);
      }
      .bg-gradient-success {
        background: linear-gradient(135deg, #6700cd 0%, #990300 100%);
      }
      .back-btn {
        position: fixed;
        bottom: 20px;
        left: 20px;
        z-index: 1000;
        transition: all 0.3s ease;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .back-btn:hover {
        transform: scale(1.1) rotate(-5deg);
      }
      .skill-badge {
        transition: all 0.3s ease;
        transform: translateY(0);
      }
      .developer-card:hover .skill-badge {
        transform: translateY(-3px);
      }
      .section-title {
        position: relative;
        padding-bottom: 10px;
      }
      .section-title::after {
        content: "";
        position: absolute;
        bottom: 0;
        right: 0;
        width: 50px;
        height: 3px;
        background: linear-gradient(to right, #990300, #6700cd);
        transition: all 0.3s ease;
      }
      .developer-card:hover .section-title::after {
        width: 80px;
      }
      .intro-text {
        position: relative;
        padding: 15px;
        background: rgba(255, 255, 255, 0.85);
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        backdrop-filter: blur(5px);
      }
      /* Header Styles */
      .intro-box {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.4s ease;
        overflow: hidden;
      }

      .intro-box:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .intro-box p {
        transition: all 0.3s ease;
        padding: 8px 0;
      }

      .intro-box:hover p {
        transform: translateX(5px);
      }

      .blur-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          45deg,
          rgba(103, 0, 205, 0.1),
          rgba(153, 3, 0, 0.1)
        );
        z-index: -1;
      }

      /* Animation for the title */
      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      h1 {
        animation: fadeInUp 0.8s ease-out forwards;
      }

      .intro-box p {
        opacity: 0;
        animation: fadeInUp 0.6s ease-out forwards;
      }

      .intro-box p:nth-child(1) {
        animation-delay: 0.3s;
      }
      .intro-box p:nth-child(2) {
        animation-delay: 0.4s;
      }
      .intro-box p:nth-child(3) {
        animation-delay: 0.5s;
      }
      .intro-box p:nth-child(4) {
        animation-delay: 0.6s;
      }
    </style>
  </head>
  <body class="bg-light">
    <!-- Back Button -->
    <a href="/index" class="back-btn btn btn-danger rounded-circle shadow-lg">
      <i class="fas fa-arrow-right fs-4"></i>
    </a>

    <div class="container py-5">
      <div class="text-center mb-5">
        <!-- Main Title -->
        <h1
          class="fw-bold display-4 mb-4 text-white position-relative"
          style="text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3)"
        >
          <span class="position-relative">
            فريق مطوري الموقع
            <span
              class="position-absolute bottom-0 start-50 translate-middle-x"
              style="
                width: 80px;
                height: 3px;
                background: linear-gradient(to right, #990300, #6700cd);
              "
            ></span>
          </span>
        </h1>

        <!-- Description Box -->
        <div
          class="intro-box mx-auto p-4 position-relative"
          style="max-width: 800px"
        >
          <div class="blur-bg"></div>
          <p class="mb-0 fs-5 text-white position-relative text-center">
            {وَقُل رَّبِّ زِدْنِي عِلْمًا} [طه: 114]
            <br />
            <i class="fas fa-paint-brush me-2" style="color: #6700cd"></i>
            بفضل الله وتوفيقه قادرون على صنع برمجيات ذكية وسهلة الاستخدام، نبدأ
            بالفكرة ونحولها إلى نظام متكامل يحل المشكلات ويحسّن الحياة.
          </p>
        </div>
      </div>
      <div class="row justify-content-center">
        <!-- First developer -->
        <div class="col-lg-5 col-md-6 mb-4">
          <div class="developer-card h-100 bg-white">
            <div
              class="bg-gradient-primary text-white text-center py-4 position-relative overflow-hidden"
            >
              <div
                class="position-absolute top-0 start-0 w-100 h-100"
                style="
                  background: radial-gradient(
                    circle at 20% 30%,
                    rgba(255, 255, 255, 0.2) 0%,
                    transparent 50%
                  );
                "
              ></div>
              <img
                src="{% static 'images/about_developers/ahmad_img.jpeg' %}"
                alt="صورة شخصية"
                class="profile-img rounded-circle mb-3"
              />
              <h3 class="position-relative">
                أحمد رباح البرغوثي
                <i
                  class="fas fa-check-circle text-white ms-2"
                  title="Verified Account"
                  style="font-size: 1.2rem"
                ></i>
              </h3>
              <p class="mb-0 position-relative">
                مهندس برمجيات شغوف || مطور ويب وتطبيقات محترف
              </p>
            </div>
            <div class="p-4">
              <h4 class="fw-bold border-bottom pb-2 section-title">
                عن المطور
              </h4>
              <p class="text-muted">
                مهندس برمجيات شغوف ومحترف، حاصل على بكالوريوس هندسة الحاسوب من
                جامعة بيرزيت.
                <br />
                أمتلك شغفاً عميقاً بفن هندسة البرمجيات وبناء الأنظمة الذكية.
                بالإضافة إلى خبرتي في تطوير حلول الويب المتكاملة، أركز على تصميم
                بنى برمجية قوية وقابلة للتوسع. أسعى دائماً إلى دمج الإبداع مع
                الأسس الهندسية المتينة لإنشاء أنظمة تعمل بكفاءة وتُحدِث تأثيراً
                حقيقياً في العالم الرقمي.
              </p>

              <h4 class="fw-bold border-bottom pb-2 mt-4 section-title">
                وسائل التواصل
              </h4>
              <div class="text-center py-2">
                <a
                  href="mailto:<EMAIL>"
                  class="social-icon bg-dark text-white text-decoration-none"
                  title="Email"
                >
                  <i class="fas fa-envelope"></i>
                </a>
                <a
                  href="https://github.com/ahmadeiss?tab=repositories"
                  class="social-icon bg-dark text-white text-decoration-none"
                  title="GitHub"
                  target="_blank"
                >
                  <i class="fab fa-github"></i>
                </a>
                <a
                  href="https://www.linkedin.com/in/ahmad-barghouthi-151155106"
                  class="social-icon bg-primary text-white text-decoration-none"
                  title="LinkedIn"
                  target="_blank"
                >
                  <i class="fab fa-linkedin-in"></i>
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Second developer -->
        <div class="col-lg-5 col-md-6 mb-4">
          <div class="developer-card h-100 bg-white">
            <div
              class="bg-gradient-success text-white text-center py-4 position-relative overflow-hidden"
            >
              <div
                class="position-absolute top-0 start-0 w-100 h-100"
                style="
                  background: radial-gradient(
                    circle at 80% 30%,
                    rgba(255, 255, 255, 0.2) 0%,
                    transparent 50%
                  );
                "
              ></div>
              <img
                src="{% static 'images/about_developers/abdalkarim_img.jpg' %}"
                alt="صورة شخصية"
                class="profile-img rounded-circle mb-3"
              />
              <h3 class="position-relative">
                عبد الكريم نائل البرغوثي
                <i
                  class="fas fa-check-circle text-white ms-2"
                  title="Verified Account"
                  style="font-size: 1.2rem"
                ></i>
              </h3>
              <p class="mb-0 position-relative">
                مهندس برمجيات شغوف || مطور ويب وتطبيقات محترف
              </p>
            </div>
            <div class="p-4">
              <h4 class="fw-bold border-bottom pb-2 section-title">
                عن المطور
              </h4>
              <p class="text-muted">
                مهندس برمجيات شغوف بالتقنية والابتكار، حاصل على بكالوريوس هندسة
                الحاسوب من جامعة بيرزيت.
                <br />
                أمتلك شغفاً عميقاً بتحويل الأفكار إلى أنظمة ذكية وقوية. إلى جانب
                خبرتي في تطوير الويب الكامل، أركز على بناء حلول برمجية متكاملة
                تُواكب أحدث التوجهات التقنية. أسعى دائماً لتعزيز مهاراتي في
                هندسة البرمجيات لإنشاء أنظمة تلبي احتياجات العصر الرقمي بجودة
                وكفاءة عالية.
              </p>

              <h4 class="fw-bold border-bottom pb-2 mt-4 section-title">
                وسائل التواصل
              </h4>
              <div class="text-center py-2">
                <a
                  href="mailto:<EMAIL>"
                  class="social-icon bg-dark text-white text-decoration-none"
                  title="Email"
                >
                  <i class="fas fa-envelope"></i>
                </a>
                <a
                  href="https://github.com/abdalkarimnael?tab=repositories"
                  class="social-icon bg-dark text-white text-decoration-none"
                  title="GitHub"
                  target="_blank"
                >
                  <i class="fab fa-github"></i>
                </a>
                <a
                  href="https://www.linkedin.com/in/anaelbarghouthi/"
                  class="social-icon bg-primary text-white text-decoration-none"
                  title="LinkedIn"
                  target="_blank"
                >
                  <i class="fab fa-linkedin-in"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Bootstrap 5.3 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
      // Add animation on scroll
      document.addEventListener("DOMContentLoaded", function () {
        const developerCards = document.querySelectorAll(".developer-card");

        developerCards.forEach((card, index) => {
          // Add delay based on index
          card.style.transitionDelay = `${index * 0.1}s`;

          // Add hover effect with JavaScript for better control
          card.addEventListener("mouseenter", function () {
            this.querySelector(".profile-img").style.transform = "scale(1.05)";
            this.querySelectorAll(".skill-badge").forEach((badge) => {
              badge.style.transform = "translateY(-3px)";
            });
          });

          card.addEventListener("mouseleave", function () {
            this.querySelector(".profile-img").style.transform = "scale(1)";
            this.querySelectorAll(".skill-badge").forEach((badge) => {
              badge.style.transform = "translateY(0)";
            });
          });
        });
      });
    </script>
  </body>
</html>
