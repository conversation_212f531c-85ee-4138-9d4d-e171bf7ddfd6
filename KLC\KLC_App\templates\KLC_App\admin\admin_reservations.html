{% extends 'KLC_App/admin/admin_base.html' %}

{% block title %}حجوزات القاعة - لوحة التحكم{% endblock %}

{% block body_class %}admin-reservations{% endblock %}

{% block extra_css %}
<!-- Flatpickr CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<!-- DataTables Responsive CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap5.min.css">

<style>
    /* Custom styles for flatpickr calendar */
    .flatpickr-calendar {
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        border-radius: 8px;
        font-family: 'Cairo', '<PERSON><PERSON><PERSON>', sans-serif;
        width: 300px !important;
        direction: rtl;
        background: #fff;
        z-index: 9999 !important;
    }

    .flatpickr-months {
        padding-top: 8px;
        padding-bottom: 8px;
        background-color:rgb(2, 77, 238);
        color: white;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
    }

    .flatpickr-month {
        height: 40px;
    }

    .flatpickr-current-month {
        font-size: 16px;
        font-weight: 600;
        padding-top: 0;
    }

    .flatpickr-current-month .flatpickr-monthDropdown-months {
        font-weight: 600;
    }

    .flatpickr-current-month .flatpickr-monthDropdown-months,
    .numInputWrapper {
        font-weight: 600;
        color: white;
    }

    .flatpickr-current-month .flatpickr-monthDropdown-months option {
        color: #333;
    }

    .flatpickr-weekdays {
        background-color: #f8f9fa;
        margin-top: 0;
    }

    .flatpickr-weekday {
        font-weight: 600;
        color: #555;
        height: 36px;
        line-height: 36px;
        background-color: #f8f9fa;
    }

    .flatpickr-days {
        padding: 6px;
        border: none;
    }

    .dayContainer {
        width: 100%;
        min-width: 100%;
        max-width: 100%;
    }

    .flatpickr-day {
        border-radius: 4px;
        font-weight: 500;
        font-size: 14px;
        height: 36px;
        line-height: 36px;
        margin: 2px;
        max-width: calc(100% / 7 - 4px);
        flex-basis: calc(100% / 7 - 4px);
        border: 1px solid transparent;
    }

    .flatpickr-day.selected {
        background-color: #28a745;
        border-color: #28a745;
        color: white;
    }

    .flatpickr-day.today {
        border-color: #28a745;
        color: #28a745;
        font-weight: bold;
    }

    .flatpickr-day:hover {
        background-color: #e9f7ef;
    }

    .flatpickr-day.flatpickr-disabled,
    .flatpickr-day.flatpickr-disabled:hover,
    .flatpickr-day.prevMonthDay,
    .flatpickr-day.nextMonthDay {
        color: rgba(72, 72, 72, 0.3);
        background: transparent;
    }

    .flatpickr-day.prevMonthDay,
    .flatpickr-day.nextMonthDay {
        visibility: visible !important;
        display: block !important;
    }

    /* Fix for RTL calendar navigation */
    .flatpickr-prev-month,
    .flatpickr-next-month {
        padding: 10px;
        fill: white !important;
    }

    .flatpickr-prev-month:hover,
    .flatpickr-next-month:hover {
        background: rgba(255, 255, 255, 0.1);
    }

    .flatpickr-prev-month svg,
    .flatpickr-next-month svg {
        fill: white !important;
    }

    /* Improve form input styling */
    .flatpickr-date {
        background-color: #fff !important;
        cursor: pointer;
    }

    /* Add calendar icon to date inputs */
    .date-input-container {
        position: relative;
    }

    .date-input-container i {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        cursor: pointer;
    }

    .date-input-container input {
        padding-left: 35px;
        cursor: pointer;
    }

    /* Fix for modal and calendar z-index */
    .modal {
        z-index: 1050;
    }

    .flatpickr-calendar.open {
        z-index: 1060 !important;
    }

    /* Professional form styling */
    .icon-circle {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(58, 124, 165, 0.1);
    }

    .bg-info-light {
        background-color: rgba(33, 150, 243, 0.1) !important;
    }

    .bg-primary-light {
        background-color: rgba(58, 124, 165, 0.1) !important;
    }

    .input-group-focus {
        box-shadow: 0 0 0 0.25rem rgba(58, 124, 165, 0.25) !important;
        transition: all 0.2s ease-in-out;
    }

    .input-group {
        transition: all 0.2s ease-in-out;
    }

    .form-control:focus {
        box-shadow: none !important;
    }

    .professional-form {
        border-radius: 12px;
        overflow: hidden;
    }

    /* Fix for date inputs in professional form */
    .professional-form .date-input-container {
        position: relative;
    }

    .professional-form .date-input-container i {
        position: absolute;
        left: auto;
        right: auto;
        top: auto;
        transform: none;
    }

    .professional-form .date-input-container input {
        padding-left: inherit;
    }
</style>
{% endblock %}

{% block content %}
<div class="section-header">
    <h2><i class="fas fa-calendar-alt me-2"></i> حجوزات القاعة</h2>
    <a href="{% url 'add_reservation' %}" class="btn btn-primary">
        <i class="fas fa-plus-circle me-2"></i> إضافة حجز جديد
    </a>
</div>

<!-- Reservations Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title">
            <i class="fas fa-calendar-check me-2"></i> قائمة الحجوزات
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table align-middle" id="reservationsTable">
                <thead>
                    <tr>
                        <th scope="col">#</th>
                        <th scope="col">رقم الهوية</th>
                        <th scope="col">الاسم</th>
                        <th scope="col">رقم الهاتف</th>
                        <th scope="col">تاريخ الإنشاء</th>
                        <th scope="col">نوع المناسبة</th>
                        <th scope="col">تاريخ البداية</th>
                        <th scope="col">تاريخ النهاية</th>
                        <th scope="col">الحالة</th>
                        <th scope="col">الأيام المتبقية للحذف</th>
                        <th scope="col">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for reservation in hall_reservations %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ reservation.id_number }}</td>
                        <td>{{ reservation.full_name }}</td>
                        <td>{{ reservation.phone_number }}</td>
                        <td>{{ reservation.created_at }}</td>
                        <td>{{ reservation.event_type }}</td>
                        <td>{{ reservation.start_date }}</td>
                        <td>{{ reservation.end_date }}</td>
                        <td>
                            {% if reservation.reservation_status == 'confirmed' %}
                                <span class="badge bg-success">مؤكد</span>
                            {% else %}
                                <span class="badge bg-warning">قيد الانتظار</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if reservation.reservation_status == 'confirmed' %}
                                <span class="badge bg-secondary">-</span>
                            {% else %}
                                {% if reservation.days_remaining == 0 %}
                                    <span class="badge bg-danger">سيتم الحذف قريباً</span>
                                {% else %}
                                    <span class="badge bg-info">{{ reservation.days_remaining }} أيام</span>
                                {% endif %}
                            {% endif %}
                        </td>
                        <td class="action-buttons">
                            {% if reservation.reservation_status != 'confirmed' %}
                            <form method="POST" action="{% url 'confirm_reservation_admin' reservation.id %}" style="display: inline;">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-sm btn-success" title="تأكيد الحجز">
                                    <i class="fas fa-check"></i>
                                </button>
                            </form>
                            {% endif %}
                            <form method="POST" action="{% url 'delete_reservation_admin' reservation.id %}" style="display: inline;">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-sm btn-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذا الحجز؟')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="11" class="text-center">لا يوجد حجوزات</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>


{% endblock %}

{% block extra_js %}
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<!-- Flatpickr JS -->
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<!-- Flatpickr Arabic Language -->
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ar.js"></script>
<!-- DataTables Responsive JS -->
<script src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>

<script>
    // Add placeholder to search input after DataTable is initialized
    $(document).ready(function () {
        // Wait for DataTable to be initialized by admin_dashboard.js
        setTimeout(function () {
            $('.dataTables_filter input').attr('placeholder', 'البحث...');
        }, 100);
    });

    // Set the active page for the sidebar
    document.addEventListener('DOMContentLoaded', function() {
        // Add active class to reservations link
        const reservationsLink = document.querySelector('a[href="{% url "admin_reservations" %}"]');
        if (reservationsLink) {
            reservationsLink.classList.add('active');
        }
    });

    // Inactivity timeout for security (5 minutes)
    (function () {
        const INACTIVITY_TIMEOUT = 300000; // 5 minutes
        let timeout;

        function logout() {
            window.location.href = '/admin_logout';
        }

        function resetTimer() {
            clearTimeout(timeout);
            timeout = setTimeout(logout, INACTIVITY_TIMEOUT);
        }

        // Set up event listeners
        ['load', 'mousemove', 'mousedown', 'click', 'scroll', 'keypress'].forEach(function (event) {
            window.addEventListener(event, resetTimer);
        });

        // Initialize timer
        resetTimer();
    })();
</script>
{% endblock %}
