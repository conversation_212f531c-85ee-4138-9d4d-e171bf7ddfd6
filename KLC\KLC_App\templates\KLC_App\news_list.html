{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
    />
    <meta name="description" content="أخبار وإنجازات مجلس قروي كفر عين" />
    <meta name="author" content="" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <title>أخبار وإنجازات مجلس قروي كفر عين</title>
    <!-- Force light mode by default -->
    <script>
      // Set light mode immediately before any other scripts run
      document.documentElement.setAttribute("data-theme", "light");
      localStorage.setItem("theme", "light");
    </script>
    <!-- Bootstrap 5.3 CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Favicon-->
    <link
      rel="icon"
      type="image/x-icon"
      href="{% static 'images/logo.png' %}"
    />
    <!-- Core theme CSS (includes Bootstrap)-->
    <link href="{% static 'css/index.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
    <!-- Theme CSS -->
    <link href="{% static 'css/theme.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
    <!-- News Section CSS -->
    <link href="{% static 'css/news.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
    <!-- Modern News Layout CSS -->
    <link href="{% static 'css/modern-news.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
    <!-- News Image Carousel CSS -->
    <link href="{% static 'css/news-image-carousel.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
      /* News List Page Specific Styles */
      .news-list-container {
        padding: 60px 0;
        background-color: #f8f9fa;
        min-height: 100vh;
      }

      .news-list-header {
        margin-bottom: 40px;
        text-align: center;
      }

      .news-list-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 15px;
      }

      .news-list-subtitle {
        font-size: 1.2rem;
        color: #666;
        margin-bottom: 30px;
      }

      .news-grid {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 30px;
        margin-bottom: 40px;
      }

      .news-item {
        background: #fff;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
        grid-column: span 2;
      }

      /* Main news item styling */
      .main-news-item {
        grid-column: span 4;
      }

      .main-news-image-container {
        height: 300px !important;
        background-color: #ffffff; /* White background */
      }

      .main-news-title {
        font-size: 1.6rem;
        margin-bottom: 20px;
      }

      .main-news-excerpt {
        font-size: 1.1rem;
        margin-bottom: 25px;
      }

      .main-news-category {
        font-size: 0.9rem;
        padding: 6px 12px;
      }

      /* Featured indicator styling */
      .featured-indicator {
        position: absolute;
        top: 10px;
        left: 10px;
        background-color: rgba(220, 53, 69, 0.9);
        color: white;
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
        z-index: 5;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        gap: 5px;
      }

      .featured-indicator i {
        color: #FFD700;
      }

      .news-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
      }

      .news-item-image-container {
        position: relative;
        height: 200px;
        overflow: hidden;
        background-color: #ffffff; /* White background */
      }

      .news-item-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
      }

      .news-item:hover .news-item-image {
        transform: scale(1.05);
      }

      .news-item-category {
        position: absolute;
        top: 15px;
        right: 15px;
        background-color: #dc3545;
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 600;
      }

      .news-item-content {
        padding: 20px;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
      }

      .news-item-date {
        font-size: 0.85rem;
        color: #6c757d;
        margin-bottom: 10px;
      }

      .news-item-title {
        font-size: 1.25rem;
        font-weight: 700;
        margin-bottom: 15px;
        color: #333;
        line-height: 1.4;
      }

      .news-item-excerpt {
        color: #666;
        margin-bottom: 20px;
        flex-grow: 1;
      }

      .news-item-footer {
        margin-top: auto;
      }

      .news-pagination {
        margin-top: 40px;
      }

      .page-link {
        color: #dc3545;
        border-color: #dee2e6;
      }

      .page-item.active .page-link {
        background-color: #dc3545;
        border-color: #dc3545;
      }

      .page-link:hover {
        color: #fff;
        background-color: #dc3545;
        border-color: #dc3545;
      }

      /* Responsive adjustments */
      @media (max-width: 1200px) {
        .news-grid {
          grid-template-columns: repeat(4, 1fr);
        }

        .news-item {
          grid-column: span 2;
        }

        .main-news-item {
          grid-column: span 4;
        }

        .main-news-image-container {
          height: 280px !important;
        }
      }

      @media (max-width: 992px) {
        .news-grid {
          grid-template-columns: repeat(2, 1fr);
        }

        .news-item {
          grid-column: span 1;
        }

        .main-news-item {
          grid-column: span 2;
        }

        .main-news-image-container {
          height: 250px !important;
        }

        .main-news-title {
          font-size: 1.4rem;
        }
      }

      @media (max-width: 768px) {
        .news-grid {
          grid-template-columns: 1fr;
        }

        .news-item, .main-news-item {
          grid-column: span 1;
        }

        .news-list-title {
          font-size: 2rem;
        }

        .main-news-image-container {
          height: 220px !important;
        }
      }

      @media (max-width: 576px) {
        .main-news-title {
          font-size: 1.3rem;
          margin-bottom: 15px;
        }

        .main-news-excerpt {
          font-size: 1rem;
          margin-bottom: 20px;
        }

        .main-news-image-container {
          height: 200px !important;
        }
      }

      /* Header styles */
      .site-header {
        background-color: #343a40;
        padding: 15px 0;
        color: white;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .logo-container {
        display: flex;
        align-items: center;
        cursor: pointer;
        text-decoration: none;
        color: white;
        transition: opacity 0.2s ease;
      }

      .logo-container:hover {
        opacity: 0.9;
        color: white;
      }

      .logo-container img {
        max-height: 60px;
        margin-left: 15px;
      }

      .logo-text {
        font-weight: 700;
        font-size: 1.5rem;
      }

      /* Responsive header styles */
      @media (max-width: 576px) {
        .site-header {
          padding: 10px 0;
        }

        .logo-container img {
          max-height: 40px;
          margin-left: 10px;
        }

        .logo-text {
          font-size: 1.1rem;
        }

        .header-btn {
          font-size: 0.8rem;
          padding: 0.375rem 0.75rem;
        }
      }
    </style>
  </head>
  <body>
    <!-- Header -->
    <header class="site-header">
      <div class="container">
        <div class="d-flex justify-content-between align-items-center">
          <a href="{% url 'index' %}" class="logo-container">
            <img src="{% static 'images/logo.png' %}" alt="شعار المجلس القروي" />
            <span class="logo-text">مجلس قروي كفر عين</span>
          </a>
          <a href="{% url 'index' %}" class="btn btn-outline-light header-btn">
            <i class="fas fa-home me-2"></i> العودة للرئيسية
          </a>
        </div>
      </div>
    </header>

    <!-- News List Content -->
<div class="news-list-container">
  <div class="container">
    <div class="news-list-header">
      <h1 class="news-list-title">أخبار وإنجازات مجلس قروي كفر عين</h1>
      <p class="news-list-subtitle">آخر الأخبار والإنجازات والفعاليات في المجلس القروي</p>
    </div>

    {% if grouped_news %}
      <div id="newsListCarousel" class="carousel slide" data-bs-interval="false" data-bs-wrap="true" data-bs-keyboard="true">
        <div class="carousel-inner">
          {% for news_page in grouped_news %}
            <div class="carousel-item {% if forloop.first %}active{% endif %}">
              <div class="news-grid">
                {% for news in news_page %}
                  {% if news.is_featured %}
                  <!-- Main news item (larger) -->
                  <div class="news-item main-news-item">
                    <div class="news-item-image-container main-news-image-container">
                      {% if news.additional_images %}
                        <!-- Image carousel for news with multiple images -->
                        <div class="news-image-carousel">
                          <!-- Main image slide -->
                          <div class="carousel-slide active">
                            <img src="{{ news.image_src }}" alt="{{ news.title }}"
                                 onerror="this.onerror=null; this.src='{% static 'images/image-not-found.jpg' %}'; console.error('Failed to load image: {{ news.image_src }}');">
                          </div>

                          <!-- Additional image slides -->
                          {% for img_src in news.additional_images %}
                            <div class="carousel-slide">
                              <img src="{{ img_src }}" alt="{{ news.title }} - صورة {{ forloop.counter|add:1 }}"
                                   onerror="this.onerror=null; this.src='{% static 'images/image-not-found.jpg' %}'; console.error('Failed to load image: {{ img_src }}');">
                            </div>
                          {% endfor %}

                          <!-- Carousel indicators -->
                          <div class="carousel-indicators">
                            <!-- Will be populated by JavaScript -->
                          </div>
                        </div>
                      {% else %}
                        <!-- Single image display -->
                        <img src="{{ news.image_src }}" alt="{{ news.title }}" class="news-item-image"
                             onerror="this.onerror=null; this.src='{% static 'images/image-not-found.jpg' %}'; console.error('Failed to load image: {{ news.image_src }}');">
                      {% endif %}

                      <span class="news-item-category main-news-category">{{ news.type|default:"أخبار" }}</span>
                    </div>
                    <div class="news-item-content">
                      <div class="news-item-date">
                        <i class="fas fa-calendar-alt me-1"></i>{{ news.published_at }}
                      </div>
                      <h3 class="news-item-title main-news-title">{{ news.title }}</h3>
                      <p class="news-item-excerpt main-news-excerpt">{{ news.Description|truncatechars:250 }}</p>
                      <div class="news-item-footer">
                        <a href="{% url 'news_detail' news.id %}" class="btn btn-outline-danger">
                          <i class="fas fa-book-open me-1"></i> اقرأ المزيد
                        </a>
                        {% if news.video_url %}
                          <a href="{% url 'news_detail' news.id %}" class="btn btn-outline-dark ms-2">
                            <i class="fas fa-video me-1"></i> شاهد الفيديو
                          </a>
                        {% endif %}
                      </div>
                    </div>
                  </div>
                  {% else %}
                  <!-- Regular news item -->
                  <div class="news-item">
                    <div class="news-item-image-container">
                      {% if news.additional_images %}
                        <!-- Image carousel for news with multiple images -->
                        <div class="news-image-carousel">
                          <!-- Main image slide -->
                          <div class="carousel-slide active">
                            <img src="{{ news.image_src }}" alt="{{ news.title }}"
                                 onerror="this.onerror=null; this.src='{% static 'images/image-not-found.jpg' %}'; console.error('Failed to load image: {{ news.image_src }}');">
                          </div>

                          <!-- Additional image slides -->
                          {% for img_src in news.additional_images %}
                            <div class="carousel-slide">
                              <img src="{{ img_src }}" alt="{{ news.title }} - صورة {{ forloop.counter|add:1 }}"
                                   onerror="this.onerror=null; this.src='{% static 'images/image-not-found.jpg' %}'; console.error('Failed to load image: {{ img_src }}');">
                            </div>
                          {% endfor %}

                          <!-- Carousel indicators -->
                          <div class="carousel-indicators">
                            <!-- Will be populated by JavaScript -->
                          </div>
                        </div>
                      {% else %}
                        <!-- Single image display -->
                        <img src="{{ news.image_src }}" alt="{{ news.title }}" class="news-item-image"
                             onerror="this.onerror=null; this.src='{% static 'images/image-not-found.jpg' %}'; console.error('Failed to load image: {{ news.image_src }}');">
                      {% endif %}

                      <span class="news-item-category">{{ news.type|default:"أخبار" }}</span>
                    </div>
                    <div class="news-item-content">
                      <div class="news-item-date">
                        <i class="fas fa-calendar-alt me-1"></i>{{ news.published_at }}
                      </div>
                      <h3 class="news-item-title">{{ news.title }}</h3>
                      <p class="news-item-excerpt">{{ news.Description|truncatechars:120 }}</p>
                      <div class="news-item-footer">
                        <a href="{% url 'news_detail' news.id %}" class="btn btn-outline-danger">
                          <i class="fas fa-book-open me-1"></i> اقرأ المزيد
                        </a>
                        {% if news.video_url %}
                          <a href="{% url 'news_detail' news.id %}" class="btn btn-outline-dark ms-2">
                            <i class="fas fa-video me-1"></i> شاهد الفيديو
                          </a>
                        {% endif %}
                      </div>
                    </div>
                  </div>
                  {% endif %}
                {% endfor %}
              </div>
            </div>
          {% endfor %}
        </div>

        {% if grouped_news|length > 1 %}
          <nav aria-label="News pagination" class="news-pagination">
            <ul class="pagination justify-content-center">
              <li class="page-item">
                <a class="page-link" href="#" data-bs-target="#newsListCarousel" data-bs-slide="prev">
                  <i class="fas fa-chevron-right"></i>
                  <span class="visually-hidden">السابق</span>
                </a>
              </li>

              {% for news_page in grouped_news %}
                <li class="page-item {% if forloop.first %}active{% endif %}">
                  <a class="page-link" href="#" data-bs-target="#newsListCarousel" data-bs-slide-to="{{ forloop.counter0 }}">
                    {{ forloop.counter }}
                  </a>
                </li>
              {% endfor %}

              <li class="page-item">
                <a class="page-link" href="#" data-bs-target="#newsListCarousel" data-bs-slide="next">
                  <i class="fas fa-chevron-left"></i>
                  <span class="visually-hidden">التالي</span>
                </a>
              </li>
            </ul>
          </nav>
        {% endif %}
      </div>
    {% else %}
      <div class="alert alert-info text-center">
        <i class="fas fa-info-circle me-2"></i> لا توجد أخبار متاحة حالياً
      </div>
    {% endif %}

      </div>
    </div>

    <!-- Bootstrap JS and dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.min.js"></script>
    <!-- News Image Carousel JS -->
    <script src="{% static 'js/news-image-carousel.js' %}?v={{ STATIC_VERSION }}"></script>

    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // Handle pagination active state
        const carousel = document.getElementById('newsListCarousel');
        if (carousel) {
          carousel.addEventListener('slide.bs.carousel', function(event) {
            const activeIndicator = document.querySelector('.pagination .active');
            if (activeIndicator) {
              activeIndicator.classList.remove('active');
            }

            const newActiveIndicator = document.querySelector(`.pagination li:nth-child(${event.to + 2})`);
            if (newActiveIndicator) {
              newActiveIndicator.classList.add('active');
            }
          });
        }
      });
    </script>
  </body>
</html>
