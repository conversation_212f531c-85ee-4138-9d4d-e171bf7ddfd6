import pandas as pd

# Define the Person class
class Person:
    def __init__(self, national_id, name, phone, address):
        self.national_id = national_id
        self.name = name
        self.phone = phone
        self.address = address

    def __str__(self):
        return f"ID: {self.national_id}, Name: {self.name}, Phone: {self.phone}, Address: {self.address}"

# Mapping for Arabic column names to English field names
PERSON_COLUMN_MAPPING = {
    "رقم الهوية": "national_id",
    "الإسم الرباعي": "name",
    "رقم الهاتف": "phone",
    "العنوان": "address",
}

# Path to the Excel file
FILE_PATH = "../Persons_Test.xlsx"

def import_excel():
    try:
        # Load Excel file
        df = pd.read_excel(FILE_PATH, engine="openpyxl")

        # Ensure necessary columns exist
        missing_cols = [col for col in PERSON_COLUMN_MAPPING if col not in df.columns]
        if missing_cols:
            print(f"Error: Missing columns in the Excel file: {missing_cols}")
            return

        # Rename columns
        df.rename(columns=PERSON_COLUMN_MAPPING, inplace=True)

        # Keep only relevant columns and drop rows with missing national_id
        df = df[list(PERSON_COLUMN_MAPPING.values())].dropna(subset=["national_id"])

        # Create a list to store Person objects
        persons = []

        # Iterate through each row and create Person objects
        for _, row in df.iterrows():
            person = Person(
                national_id=row["national_id"],
                name=row["name"],
                phone=row["phone"],
                address=row["address"]
            )
            persons.append(person)

        # Print all created Person objects
        print("✅ Persons imported successfully:")
        for person in persons:
            print(person)

    except FileNotFoundError:
        print("❌ Error: The specified file was not found.")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

# Run the import function
import_excel()
