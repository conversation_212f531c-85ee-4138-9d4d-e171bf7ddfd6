{% extends 'KLC_App/admin/admin_base.html' %}

{% block title %}تعديل الخبر - لوحة التحكم{% endblock %}

{% block body_class %}admin-news{% endblock %}

{% block content %}
<div class="section-header d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-edit me-2"></i> تعديل الخبر</h2>
    <a href="{% url 'admin_news' %}" class="btn btn-outline-primary">
        <i class="fas fa-arrow-right me-2"></i> العودة إلى قائمة الأخبار
    </a>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow-sm">
            <div class="card-body">
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <form method="POST" enctype="multipart/form-data" class="admin-form">
                    {% csrf_token %}

                    <div class="mb-4">
                        <h3 class="mb-3 text-primary">تعديل بيانات الخبر</h3>
                        <p class="text-muted mb-4">يمكنك تعديل بيانات الخبر من خلال النموذج أدناه</p>
                    </div>

                    <div class="alert alert-warning mb-4">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكد من صحة البيانات قبل حفظ التعديلات.
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.title.id_for_label }}" class="form-label">{{ form.title.label }} <span class="text-danger">*</span></label>
                        {{ form.title }}
                        {% if form.title.errors %}
                            <div class="text-danger mt-1">{{ form.title.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }} <span class="text-danger">*</span></label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger mt-1">{{ form.description.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.image.id_for_label }}" class="form-label">{{ form.image.label }}</label>
                        {% if current_image %}
                        <div class="mb-2">
                            <img src="{{ current_image }}" alt="Current Image" class="img-thumbnail" style="max-height: 150px;">
                            <p class="form-text">الصورة الرئيسية الحالية</p>
                        </div>
                        {% endif %}
                        {{ form.image }}
                        <div class="form-text">اترك هذا الحقل فارغاً إذا كنت لا ترغب في تغيير الصورة الرئيسية الحالية</div>
                        {% if form.image.errors %}
                            <div class="text-danger mt-1">{{ form.image.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.additional_images.id_for_label }}" class="form-label">{{ form.additional_images.label }}</label>
                        {% if current_additional_images %}
                        <div class="mb-2">
                            <div class="row">
                                {% for img_url in current_additional_images %}
                                <div class="col-md-3 col-sm-4 col-6 mb-2">
                                    <img src="{{ img_url }}" alt="Additional Image {{ forloop.counter }}" class="img-thumbnail" style="height: 100px; width: 100%; object-fit: cover;">
                                </div>
                                {% endfor %}
                            </div>
                            <p class="form-text">الصور الإضافية الحالية</p>
                        </div>
                        {% endif %}
                        {{ form.additional_images }}
                        <div class="form-text">{{ form.additional_images.help_text }}</div>
                        <div class="form-text">اترك هذا الحقل فارغاً إذا كنت لا ترغب في تغيير الصور الإضافية الحالية. إذا قمت بتحميل صور جديدة، سيتم استبدال الصور الحالية.</div>
                        {% if form.additional_images.errors %}
                            <div class="text-danger mt-1">{{ form.additional_images.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.video_url.id_for_label }}" class="form-label">{{ form.video_url.label }}</label>
                        {{ form.video_url }}
                        <div class="form-text">يمكنك إضافة رابط فيديو من Google Drive (اختياري)</div>
                        {% if form.video_url.errors %}
                            <div class="text-danger mt-1">{{ form.video_url.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.news_type.id_for_label }}" class="form-label">{{ form.news_type.label }}</label>
                        {{ form.news_type }}
                        <div class="form-text">مثال: أخبار، إنجازات، مشاريع، فعاليات</div>
                        {% if form.news_type.errors %}
                            <div class="text-danger mt-1">{{ form.news_type.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-4">
                        <div class="featured-news-option">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="featured-icon me-3">
                                            <i class="fas fa-star"></i>
                                        </div>
                                        <div class="featured-content">
                                            <h5 class="mb-1">خيارات العرض</h5>
                                            <div class="form-check form-switch">
                                                {{ form.is_featured }}
                                                <label class="form-check-label fw-bold" for="{{ form.is_featured.id_for_label }}">
                                                    {{ form.is_featured.label }}
                                                </label>
                                            </div>
                                            <div class="form-text text-muted mt-1">
                                                <i class="fas fa-info-circle me-1"></i>
                                                {{ form.is_featured.help_text }}
                                            </div>
                                            {% if form.is_featured.errors %}
                                                <div class="text-danger mt-2">
                                                    <i class="fas fa-exclamation-circle me-1"></i>
                                                    {{ form.is_featured.errors }}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <style>
                        .featured-news-option .card {
                            border-radius: 10px;
                            transition: all 0.3s ease;
                        }

                        .featured-news-option .card:hover {
                            transform: translateY(-3px);
                            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
                        }

                        .featured-icon {
                            width: 48px;
                            height: 48px;
                            background-color: #fff3cd;
                            color: #ffc107;
                            border-radius: 12px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 1.5rem;
                        }

                        [data-theme="dark"] .featured-icon {
                            background-color: rgba(255, 193, 7, 0.2);
                        }

                        .form-switch .form-check-input {
                            width: 3em;
                            height: 1.5em;
                            margin-top: 0.25em;
                            cursor: pointer;
                        }

                        .form-switch .form-check-input:checked {
                            background-color: #198754;
                            border-color: #198754;
                        }

                        .form-switch .form-check-label {
                            cursor: pointer;
                            padding-top: 0.25em;
                        }
                    </style>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i> حفظ التعديلات
                        </button>
                        <a href="{% url 'admin_news' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i> إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Set the active page for the sidebar
    document.addEventListener('DOMContentLoaded', function() {
        // Add active class to news link
        const newsLink = document.querySelector('a[href="{% url "admin_news" %}"]');
        if (newsLink) {
            newsLink.classList.add('active');
        }

        // Handle featured checkbox
        const featuredCheckbox = document.getElementById('{{ form.is_featured.id_for_label }}');
        if (featuredCheckbox) {
            featuredCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    // Show confirmation dialog
                    if (confirm('تحديد هذا الخبر كخبر رئيسي سيؤدي إلى إلغاء تحديد أي خبر آخر كخبر رئيسي. هل تريد المتابعة؟')) {
                        // User confirmed
                    } else {
                        // User canceled
                        this.checked = false;
                    }
                }
            });
        }

        // Preview main image when selected
        const imageInput = document.getElementById('{{ form.image.id_for_label }}');
        if (imageInput) {
            imageInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        // Create or update image preview
                        let preview = document.getElementById('image-preview');
                        if (!preview) {
                            preview = document.createElement('img');
                            preview.id = 'image-preview';
                            preview.className = 'img-fluid mt-2 border rounded';
                            preview.style.maxHeight = '200px';
                            imageInput.parentNode.appendChild(preview);
                        }
                        preview.src = e.target.result;
                    }
                    reader.readAsDataURL(this.files[0]);
                }
            });
        }

        // Preview additional images when selected
        const additionalImagesInput = document.getElementById('{{ form.additional_images.id_for_label }}');
        if (additionalImagesInput) {
            additionalImagesInput.addEventListener('change', function() {
                // Remove existing previews
                const existingPreviewContainer = document.getElementById('additional-images-preview');
                if (existingPreviewContainer) {
                    existingPreviewContainer.remove();
                }

                if (this.files && this.files.length > 0) {
                    // Create preview container
                    const previewContainer = document.createElement('div');
                    previewContainer.id = 'additional-images-preview';
                    previewContainer.className = 'row mt-2';

                    // Create preview for each file
                    Array.from(this.files).forEach((file, index) => {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const colDiv = document.createElement('div');
                            colDiv.className = 'col-md-3 col-sm-4 col-6 mb-2';

                            const img = document.createElement('img');
                            img.src = e.target.result;
                            img.className = 'img-thumbnail';
                            img.style.height = '100px';
                            img.style.width = '100%';
                            img.style.objectFit = 'cover';
                            img.alt = `Preview ${index + 1}`;

                            colDiv.appendChild(img);
                            previewContainer.appendChild(colDiv);
                        }
                        reader.readAsDataURL(file);
                    });

                    // Add preview container after input
                    additionalImagesInput.parentNode.appendChild(previewContainer);
                }
            });
        }
    });
</script>
{% endblock %}
