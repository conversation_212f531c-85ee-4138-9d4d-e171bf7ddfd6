server {
    listen 80;
    server_name ************;  # Replace with your domain or IP address

    # Redirect HTTP to HTTPS (uncomment when you have SSL set up)
    # return 301 https://$host$request_uri;

    # For HTTP-only setup (comment out if using HTTPS redirect above)
    location /static/ {
        alias /path/to/your/KLC_django/staticfiles/;  # Replace with your actual static files path
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
    }

    location /media/ {
        alias /path/to/your/KLC_django/media/;  # Replace with your actual media files path
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
    }

    location / {
        proxy_pass http://127.0.0.1:8000;  # Forward to Django running on port 8000
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Additional security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
}

# HTTPS server (uncomment when you have SSL certificates)
# server {
#     listen 443 ssl;
#     server_name ************;  # Replace with your domain or IP address
#
#     ssl_certificate /path/to/your/certificate.crt;
#     ssl_certificate_key /path/to/your/private.key;
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers HIGH:!aNULL:!MD5;
#     ssl_prefer_server_ciphers on;
#     ssl_session_cache shared:SSL:10m;
#
#     location /static/ {
#         alias /path/to/your/KLC_django/staticfiles/;  # Replace with your actual static files path
#         expires 30d;
#         add_header Cache-Control "public, max-age=2592000";
#     }
#
#     location /media/ {
#         alias /path/to/your/KLC_django/media/;  # Replace with your actual media files path
#         expires 30d;
#         add_header Cache-Control "public, max-age=2592000";
#     }
#
#     location / {
#         proxy_pass http://127.0.0.1:8000;  # Forward to Django running on port 8000
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#     }
#
#     # Additional security headers
#     add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
#     add_header X-Content-Type-Options nosniff;
#     add_header X-Frame-Options DENY;
#     add_header X-XSS-Protection "1; mode=block";
# }
