/* News Detail Page Styles */

/* News Image Container */
.news-image-container {
    margin-bottom: 2rem;
}

/* Single News Image */
.news-image {
    position: relative;
    height: 500px;
    overflow: hidden;
    border-radius: 8px;
    background-color: #000;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* News Detail Carousel */
.news-detail-carousel {
    position: relative;
    height: 600px;
    overflow: hidden;
    border-radius: 8px;
    background-color: #000;
}

/* Card styling */
.news-image-container .card {
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: 8px;
    overflow: hidden;
}

.news-image-container .card:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.news-detail-carousel .carousel-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.8s ease;
    z-index: 1;
}

.news-detail-carousel .carousel-slide.active {
    opacity: 1;
    z-index: 2;
}

.news-detail-carousel .carousel-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Carousel Controls */
.news-detail-carousel .carousel-control-prev,
.news-detail-carousel .carousel-control-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    cursor: pointer;
    border: none;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.news-detail-carousel .carousel-control-prev {
    left: 15px;
}

.news-detail-carousel .carousel-control-next {
    right: 15px;
}

.news-detail-carousel .carousel-control-prev:hover,
.news-detail-carousel .carousel-control-next:hover {
    opacity: 1;
}

.news-detail-carousel .carousel-control-prev-icon,
.news-detail-carousel .carousel-control-next-icon {
    width: 20px;
    height: 20px;
    background-size: 100% 100%;
}

/* Carousel Indicators */
.news-detail-carousel .carousel-indicators {
    position: absolute;
    bottom: 15px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    gap: 8px;
    z-index: 10;
    padding: 5px 0;
    margin: 0;
}

.news-detail-carousel .carousel-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
}

.news-detail-carousel .carousel-indicator.active {
    background-color: #dd0606;
    transform: scale(1.2);
}



/* News Content */
.news-detail-content {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.news-detail-description {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #333;
}

/* Related News */
.related-news-section {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.related-news-title {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: #333;
    border-bottom: 2px solid #dc3545;
    padding-bottom: 0.5rem;
}

.related-news-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.related-news-img-container {
    height: 180px;
    overflow: hidden;
}

.related-news-img-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .news-detail-carousel {
        height: 350px;
    }
}

@media (max-width: 768px) {
    .news-detail-carousel {
        height: 300px;
    }

    .news-detail-content {
        padding: 1.5rem;
    }

    .related-news-section {
        margin-top: 2rem;
    }
}

@media (max-width: 576px) {
    .news-detail-carousel {
        height: 250px;
    }

    .news-detail-carousel .carousel-control-prev,
    .news-detail-carousel .carousel-control-next {
        width: 35px;
        height: 35px;
    }

    .news-detail-content {
        padding: 1rem;
    }

    .news-detail-description {
        font-size: 1rem;
        line-height: 1.6;
    }
}
