/* Professional Services Page Styles */

/* Main layout and background */
body {
    background-color: var(--bg-primary);
    background-image: url('../images/patterns/subtle_dots.png');
    background-attachment: fixed;
    padding-top: 6rem;
    font-family: var(--bs-body-font-family);
}

/* Services section styling */
#services-section {
    position: relative;
    margin-top: 2rem !important;
    padding: 2.5rem 0 !important;
    border-radius: 15px;
    overflow: hidden;
    background: linear-gradient(135deg, rgba(33, 37, 41, 0.85) 0%, rgba(52, 58, 64, 0.85) 100%);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

#services-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('../images/services-background.jpg') center center/cover no-repeat;
    opacity: 0.15;
    z-index: -1;
}

/* Services title styling */
.services-title {
    position: relative;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    color: #ffffff;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.services-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #dc3545 0%, #e74c3c 100%);
    border-radius: 2px;
}

.services-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 400;
    margin-bottom: 2rem;
}

/* Service cards styling */
.service-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1.5rem;
    position: relative;
    z-index: 1;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.service-card:hover::before {
    opacity: 1;
}

.service-card img {
    width: 100px;
    height: 100px;
    object-fit: contain;
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
}

.service-card:hover img {
    transform: scale(1.1);
}

.service-card span {
    color: #ffffff;
    font-weight: 600;
    font-size: 1.2rem;
    margin-top: 0.5rem;
    text-align: center;
}

/* New service badge */
.service-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    border-radius: 30px;
    box-shadow: 0 3px 10px rgba(52, 152, 219, 0.3);
    transition: all 0.3s ease;
    z-index: 2;
}

.service-card:hover .service-badge {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.5);
}

/* Complaints and suggestions section */
#complaints-suggestions {
    margin-top: 3rem;
    margin-bottom: 3rem;
}

.complaints-box {
    background: linear-gradient(135deg, rgba(33, 37, 41, 0.9) 0%, rgba(52, 58, 64, 0.9) 100%);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    border: 2px solid #dc3545;
    position: relative;
    overflow: hidden;
}

.complaints-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('../images/patterns/subtle_dots.png');
    opacity: 0.05;
    z-index: 0;
}

.complaints-title {
    position: relative;
    color: #ffffff;
    margin-bottom: 1rem;
    z-index: 1;
}

.complaints-subtitle {
    color: rgba(255, 255, 255, 0.9);
    position: relative;
    z-index: 1;
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid rgba(220, 53, 69, 0.5);
}

/* Form styling */
.form-control {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: rgba(220, 53, 69, 0.5);
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
    color: #ffffff;
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-label {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.btn-submit {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: none;
    border-radius: 8px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.btn-submit:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
    background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .service-card {
        padding: 1.25rem;
    }
    
    .service-card img {
        width: 80px;
        height: 80px;
    }
    
    .service-card span {
        font-size: 1.1rem;
    }
}

@media (max-width: 768px) {
    #services-section {
        padding: 2rem 0 !important;
    }
    
    .services-title {
        font-size: 1.5rem;
    }
    
    .services-subtitle {
        font-size: 1rem;
    }
    
    .complaints-box {
        padding: 1.5rem;
    }
}

@media (max-width: 576px) {
    .service-card {
        margin-bottom: 1rem;
    }
    
    .service-card img {
        width: 70px;
        height: 70px;
    }
    
    .service-card span {
        font-size: 1rem;
    }
}

/* Dark mode support */
[data-theme="dark"] #services-section {
    background: linear-gradient(135deg, rgba(26, 29, 32, 0.85) 0%, rgba(44, 48, 52, 0.85) 100%);
}

[data-theme="dark"] .complaints-box {
    background: linear-gradient(135deg, rgba(26, 29, 32, 0.9) 0%, rgba(44, 48, 52, 0.9) 100%);
}
