/* Professional Modern News Grid Layout Styles */

/* News Grid Container */
.news-grid-container {
    margin-top: 2rem;
    position: relative;
}

/* News Carousel */
#newsCarousel {
    position: relative;
    margin-bottom: 3rem;
}

/* Featured News Card (Large) */
.featured-news-card {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
    height: 100%;
    transition: all 0.4s ease;
}

.featured-news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
}

.featured-img-container {
    position: relative;
    overflow: hidden;
    min-height: 400px; /* Changed from fixed height to min-height */
    max-height: 600px; /* Added max-height for very large screens */
    height: auto; /* Allow container to adjust based on content */
}

.featured-img-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s ease;
}

.featured-news-card:hover .featured-img-container img {
    transform: scale(1.05);
}

.featured-news-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 3rem 2rem 2rem;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.95), rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.3), transparent);
    color: white;
    text-align: right;
}

.featured-news-category {
    display: inline-block;
    background-color: #e74c3c;
    color: white;
    padding: 0.4rem 1rem;
    border-radius: 30px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

.featured-news-date {
    font-size: 0.95rem;
    opacity: 0.9;
    margin-bottom: 0.75rem;
    font-weight: 500;
}

.featured-news-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.3;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.featured-news-excerpt {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    opacity: 0.95;
}

.featured-news-buttons {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    position: relative;
    z-index: 10; /* Ensure buttons are above other elements */
}

.featured-news-buttons .btn {
    padding: 0.5rem 1.25rem;
    font-weight: 600;
    border-radius: 30px;
    font-size: 1rem;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    pointer-events: auto; /* Ensure buttons are always clickable */
    position: relative; /* Create a new stacking context */
}

.featured-news-buttons .btn:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    /* Removed transform to prevent button movement */
}

/* Medium News Card */
.medium-news-card {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
    height: auto; /* Changed from 100% to auto to fit content */
    min-height: 120px; /* Minimum height for consistency */
    transition: all 0.4s ease;
    background-color: white;
    display: flex; /* Use flexbox for better content distribution */
    flex-direction: column;
}

.medium-news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.medium-img-container {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.medium-img-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s ease;
}

.medium-news-card:hover .medium-img-container img {
    transform: scale(1.05);
}

.news-category-tag {
    position: absolute;
    top: 12px;
    right: 12px;
    background-color: #e74c3c;
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 30px;
    font-size: 0.8rem;
    font-weight: 600;
    box-shadow: 0 2px 6px rgba(231, 76, 60, 0.3);
    z-index: 2;
}

.medium-news-content {
    padding: 1.5rem;
    text-align: right;
    flex: 1; /* Allow content to take available space */
    display: flex;
    flex-direction: column;
}

.news-date {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.75rem;
    font-weight: 500;
}

.medium-news-title {
    font-size: 1.2rem;
    font-weight: 700;
    margin: 0.5rem 0 1rem;
    line-height: 1.4;
    min-height: 1.4rem; /* Minimum height for one line */
    max-height: 3.4rem; /* Maximum height for two lines */
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2; /* Standard property for compatibility */
    -webkit-box-orient: vertical;
    color: #2c3e50;
}

.medium-news-buttons {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    margin-top: auto; /* Push buttons to the bottom */
}

.medium-news-buttons .btn {
    padding: 0.4rem 1rem;
    font-weight: 600;
    border-radius: 30px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.medium-news-buttons .btn:hover {
    transform: translateY(-3px);
}

/* Small News Card */
.small-news-card {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
    height: auto; /* Changed from 100% to auto to fit content */
    min-height: 120px; /* Minimum height for consistency */
    transition: all 0.4s ease;
    background-color: white;
    display: flex; /* Use flexbox for better content distribution */
    flex-direction: column;
}

.small-news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.small-img-container {
    position: relative;
    height: 100%;
    min-height: 140px; /* Reduced minimum height */
    max-height: 180px; /* Maximum height */
    overflow: hidden;
}

.small-img-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s ease;
}

.small-news-card:hover .small-img-container img {
    transform: scale(1.05);
}

.small-news-category {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #e74c3c;
    color: white;
    padding: 0.25rem 0.7rem;
    border-radius: 30px;
    font-size: 0.75rem;
    font-weight: 600;
    box-shadow: 0 2px 6px rgba(231, 76, 60, 0.3);
    z-index: 2;
}

.small-news-content {
    padding: 1.25rem;
    text-align: right;
    flex: 1; /* Allow content to take available space */
    display: flex;
    flex-direction: column;
}

.small-news-date {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.small-news-title {
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    line-height: 1.4;
    min-height: 1.4rem; /* Minimum height for one line */
    max-height: 3rem; /* Maximum height for two lines */
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2; /* Standard property for compatibility */
    -webkit-box-orient: vertical;
    color: #2c3e50;
}

.small-news-excerpt {
    font-size: 0.9rem;
    line-height: 1.5;
    color: #6c757d;
    margin-bottom: 1rem;
    min-height: 1.5rem; /* Minimum height for one line */
    max-height: 4.5rem; /* Maximum height for three lines */
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3; /* Standard property for compatibility */
    -webkit-box-orient: vertical;
    flex: 1; /* Allow excerpt to take available space */
}

.small-news-buttons {
    display: flex;
    justify-content: space-between;
    gap: 8px;
    margin-top: auto; /* Push buttons to the bottom */
}

.small-news-buttons .btn {
    padding: 0.35rem 0.9rem;
    font-weight: 600;
    border-radius: 30px;
    font-size: 0.85rem;
    transition: all 0.3s ease;
}

.small-news-buttons .btn:hover {
    transform: translateY(-3px);
}

/* Carousel Navigation Controls */
.news-navigation-controls {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
    z-index: 20;
    pointer-events: auto;
    width: 100%;
}

.carousel-control-prev,
.carousel-control-next {
    width: 50px;
    height: 50px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
    transition: all 0.3s ease;
    pointer-events: auto;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    border: 2px solid #f8f9fa;
    z-index: 20;
    position: relative;
    margin: 0 5px;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    opacity: 1;
    background-color: white;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    transform: scale(1.1);
    border-color: #e74c3c;
}

.carousel-control-prev:active,
.carousel-control-next:active {
    transform: scale(0.95);
    background-color: #f8f9fa;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.carousel-control-icon {
    color: #2c3e50;
    font-size: 1.2rem;
}

.carousel-control-prev.disabled,
.carousel-control-next.disabled {
    opacity: 0.4;
    cursor: not-allowed;
    pointer-events: none;
    background-color: rgba(200, 200, 200, 0.7);
    box-shadow: none;
}

/* Professional News Pagination */
.news-pagination-container {
    margin: 2.5rem 0 1.5rem;
    position: relative;
    z-index: 10;
}

.news-pagination-info {
    margin-bottom: 1rem;
}

.page-info {
    display: inline-flex;
    align-items: center;
    background-color: #f8f9fa;
    color: #495057;
    padding: 0.6rem 1.5rem;
    border-radius: 30px;
    font-weight: 600;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
    font-size: 1.1rem;
}

/* Autoplay control button */
.autoplay-control {
    border-radius: 50%;
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    background-color: white;
    border-color: #ced4da;
}

.autoplay-control:hover {
    transform: scale(1.1);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.autoplay-control:focus {
    box-shadow: 0 0 0 0.25rem rgba(108, 117, 125, 0.25);
}

.autoplay-control.playing .fa-play {
    display: none;
}

.autoplay-control.paused .fa-pause {
    display: none;
}

.news-pagination {
    margin: 1.5rem 0;
}

.pagination {
    gap: 0.5rem;
}

.page-item .page-link {
    border-radius: 8px;
    padding: 0.8rem 1.2rem;
    color: #495057;
    font-weight: 600;
    border: 1px solid #dee2e6;
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    background-color: white;
    font-size: 1rem;
    min-width: 45px;
    text-align: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.page-item .page-link:hover {
    background-color: #f8f9fa;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    color: #212529;
    border-color: #ced4da;
    z-index: 3;
}

.page-item.active .page-link {
    background-color: #e74c3c;
    border-color: #e74c3c;
    color: white;
    box-shadow: 0 4px 10px rgba(231, 76, 60, 0.3);
    transform: translateY(-2px);
    z-index: 3;
}

.page-item.disabled .page-link {
    color: #adb5bd;
    pointer-events: none;
    background-color: #f8f9fa;
    border-color: #dee2e6;
    box-shadow: none;
}

#prevPageBtn, #nextPageBtn {
    padding: 0.8rem 1.5rem;
    font-weight: 600;
}

/* Dark theme support for pagination */
[data-theme="dark"] .page-info {
    background-color: #2c3e50;
    color: #ecf0f1;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .page-item .page-link {
    background-color: #34495e;
    border-color: #2c3e50;
    color: #ecf0f1;
}

[data-theme="dark"] .page-item .page-link:hover {
    background-color: #3d566e;
    border-color: #34495e;
}

[data-theme="dark"] .page-item.active .page-link {
    background-color: #e74c3c;
    border-color: #c0392b;
}

[data-theme="dark"] .page-item.disabled .page-link {
    background-color: #2c3e50;
    border-color: #2c3e50;
    color: #7f8c8d;
}

/* Dark Theme Support */
[data-theme="dark"] .medium-news-card,
[data-theme="dark"] .small-news-card {
    background-color: #2c3e50;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .medium-news-title,
[data-theme="dark"] .small-news-title {
    color: #ecf0f1;
}

[data-theme="dark"] .small-news-excerpt {
    color: #bdc3c7;
}

[data-theme="dark"] .small-news-date,
[data-theme="dark"] .news-date {
    color: #bdc3c7;
}

[data-theme="dark"] .carousel-control-prev,
[data-theme="dark"] .carousel-control-next {
    background-color: rgba(44, 62, 80, 0.9);
    border-color: #34495e;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .carousel-control-icon {
    color: #ecf0f1;
}

[data-theme="dark"] .carousel-control-prev:hover,
[data-theme="dark"] .carousel-control-next:hover {
    background-color: #34495e;
    border-color: #e74c3c;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .carousel-control-prev.disabled,
[data-theme="dark"] .carousel-control-next.disabled {
    opacity: 0.4;
    background-color: rgba(30, 40, 50, 0.7);
    box-shadow: none;
}

/* Row adjustments for news cards */
.news-grid-container .row {
    display: flex;
    flex-wrap: wrap;
}

.news-grid-container .row > [class*="col-"] {
    display: flex;
    flex-direction: column;
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
    .featured-img-container {
        min-height: 380px;
        max-height: 500px;
    }

    .featured-news-title {
        font-size: 1.8rem;
    }
}

@media (max-width: 992px) {
    .featured-img-container {
        min-height: 350px;
        max-height: 450px;
    }

    .featured-news-title {
        font-size: 1.6rem;
    }

    .featured-news-excerpt {
        font-size: 1rem;
    }

    .medium-img-container {
        height: 180px;
    }
}

@media (max-width: 768px) {
    .featured-img-container {
        min-height: 300px;
        max-height: 400px;
    }

    .featured-news-title {
        font-size: 1.4rem;
    }

    .featured-news-excerpt {
        font-size: 0.95rem;
        margin-bottom: 1rem;
    }

    .featured-news-buttons .btn {
        padding: 0.4rem 1rem;
        font-size: 0.9rem;
    }

    .medium-img-container {
        height: 160px;
    }

    .small-img-container {
        min-height: 160px;
    }

    .carousel-control-prev,
    .carousel-control-next {
        width: 40px;
        height: 40px;
    }
}

@media (max-width: 576px) {
    .featured-img-container {
        min-height: 250px;
        max-height: 350px;
    }

    .featured-news-overlay {
        padding: 2rem 1.5rem 1.5rem;
    }

    .featured-news-title {
        font-size: 1.3rem;
        margin-bottom: 0.75rem;
    }

    .featured-news-excerpt {
        font-size: 0.9rem;
        -webkit-line-clamp: 2;
        line-clamp: 2; /* Standard property for compatibility */
        height: 2.7rem;
        margin-bottom: 0.75rem;
    }

    .featured-news-buttons .btn {
        padding: 0.35rem 0.9rem;
        font-size: 0.85rem;
    }

    .medium-news-title,
    .small-news-title {
        font-size: 1rem;
    }

    .small-news-excerpt {
        font-size: 0.85rem;
        height: 3.8rem;
    }

    .carousel-control-prev,
    .carousel-control-next {
        width: 35px;
        height: 35px;
    }

    .carousel-control-icon {
        font-size: 1rem;
    }
}
