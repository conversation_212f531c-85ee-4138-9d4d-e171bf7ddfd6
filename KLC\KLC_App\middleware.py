from django.shortcuts import redirect

class AdminLoginRequiredMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        if request.path.startswith('/admin') and not request.path.startswith('/admin_login'):
            if not request.session.get('is_admin_authenticated'):
                return redirect('admin_login')
        response = self.get_response(request)
        return response
