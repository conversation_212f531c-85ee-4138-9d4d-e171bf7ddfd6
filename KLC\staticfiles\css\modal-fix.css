/* Modal Fix CSS
   This file fixes issues with modals in the KLC project
   Specifically addressing the issue where modals disable the entire screen
*/

/* Fix for modal backdrop to not disable the entire screen and make it transparent */
.modal-backdrop {
    pointer-events: none !important;
    background-color: transparent !important;
    opacity: 0 !important;
}

/* Fix for modal to not disable the entire screen */
.modal {
    pointer-events: none !important;
}

/* Fix for modal dialog to be clickable and have better appearance */
.modal-dialog {
    pointer-events: auto !important;
    margin-top: 5rem !important;
    max-width: 550px !important;
}

/* Fix for modal content to be clickable and stand out better */
.modal-content {
    pointer-events: auto !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
    border: 1px solid rgba(0, 0, 0, 0.2) !important;
}

/* Fix for modal body to be clickable */
.modal-body {
    pointer-events: auto !important;
}

/* Fix for modal footer to be clickable */
.modal-footer {
    pointer-events: auto !important;
}

/* Fix for modal header to be clickable */
.modal-header {
    pointer-events: auto !important;
}

/* Fix for modal close button to be clickable */
.modal-header .btn-close {
    pointer-events: auto !important;
}

/* Fix for modal buttons to be clickable */
.modal-footer .btn {
    pointer-events: auto !important;
}

/* Fix for modal form elements to be clickable */
.modal-body form,
.modal-body input,
.modal-body select,
.modal-body textarea,
.modal-body button {
    pointer-events: auto !important;
}

/* Fix for modal to not add padding to body */
body.modal-open {
    padding-right: 0 !important;
    overflow: auto !important;
}

/* Fix for modal to not add scrollbar to body */
.modal-open .modal {
    overflow-x: hidden;
    overflow-y: auto;
    padding-right: 0 !important;
}

/* Fix for modal backdrop in show state - make it completely transparent */
.modal-backdrop.show {
    opacity: 0 !important;
    pointer-events: none !important;
}

/* Fix for modal in show state */
.modal.show {
    opacity: 1;
    pointer-events: none !important;
}

/* Fix for modal dialog in show state */
.modal.show .modal-dialog {
    opacity: 1;
    pointer-events: auto !important;
}

/* Fix for modal content in show state */
.modal.show .modal-content {
    opacity: 1;
    pointer-events: auto !important;
}

/* Fix for modal body in show state */
.modal.show .modal-body {
    opacity: 1;
    pointer-events: auto !important;
}

/* Fix for modal footer in show state */
.modal.show .modal-footer {
    opacity: 1;
    pointer-events: auto !important;
}

/* Fix for modal header in show state */
.modal.show .modal-header {
    opacity: 1;
    pointer-events: auto !important;
}

/* Fix for modal close button in show state */
.modal.show .modal-header .btn-close {
    opacity: 1;
    pointer-events: auto !important;
}

/* Fix for modal buttons in show state */
.modal.show .modal-footer .btn {
    opacity: 1;
    pointer-events: auto !important;
}

/* Fix for modal form elements in show state */
.modal.show .modal-body form,
.modal.show .modal-body input,
.modal.show .modal-body select,
.modal.show .modal-body textarea,
.modal.show .modal-body button {
    opacity: 1;
    pointer-events: auto !important;
}

/* Fix for modal backdrop to not disable the entire screen */
.modal-open .modal-backdrop {
    pointer-events: none !important;
    background-color: transparent !important;
    opacity: 0 !important;
}

/* Fix for modal to not disable the entire screen */
.modal-open {
    pointer-events: auto !important;
    background-color: transparent !important;
}

/* Fix for modal to not disable the entire screen */
body.modal-open {
    pointer-events: auto !important;
    background-color: transparent !important;
    filter: none !important;
    -webkit-filter: none !important;
}
