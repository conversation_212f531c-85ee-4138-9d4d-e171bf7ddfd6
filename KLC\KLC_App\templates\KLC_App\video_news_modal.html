<!-- Professional Modal for the news and achievements video -->
<div
  class="modal fade"
  id="videoModal"
  tabindex="-1"
  aria-labelledby="videoModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content video-modal-content">
      <div class="modal-header video-modal-header">
        <button
          id="close-video-modal"
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
        <h5 class="modal-title" id="videoModalLabel">فيديو توضيحي</h5>
      </div>
      <div class="modal-body video-modal-body">
        <div class="video-modal-meta">
          <div class="video-modal-date">
            <i class="fas fa-calendar-alt me-1"></i>
            <span class="date-label">تاريخ النشر:</span>
            <span class="date-value"><!-- Date will be inserted here --></span>
          </div>
          <div class="video-modal-category">
            <i class="fas fa-tag me-1"></i>
            <span class="category-value">فيديو</span>
          </div>
        </div>
        <div class="video-modal-description">
          <!-- Description will be inserted here -->
        </div>
        <div class="professional-video-container">
          <div class="video-loading-indicator">
            <div class="spinner-border text-danger" role="status">
              <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p>جاري تحميل الفيديو...</p>
          </div>
          <iframe
            src=""
            title="Video"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowfullscreen
            class="video-iframe"
            onload="this.parentNode.classList.add('loaded')"
          ></iframe>
        </div>
      </div>
      <div class="modal-footer video-modal-footer">
        <button type="button" class="btn btn-secondary video-close-btn" data-bs-dismiss="modal">
          <i class="fas fa-times me-1"></i> إغلاق
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Add CSS for the professional video modal -->
<style>
  .video-modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.2);
    overflow: hidden;
  }

  .video-modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1.25rem 1.5rem;
  }

  .video-modal-header .modal-title {
    font-weight: 700;
    color: #212529;
    font-size: 1.5rem;
    position: relative;
    padding-right: 15px;
  }

  .video-modal-header .modal-title::before {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 5px;
    height: 70%;
    background-color: #dc3545;
    border-radius: 3px;
  }

  .video-modal-body {
    padding: 1.5rem;
    background-color: #fff;
  }

  .video-modal-meta {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 15px 20px;
    margin-bottom: 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border-right: 4px solid #dc3545;
  }

  .video-modal-date, .video-modal-category {
    display: flex;
    align-items: center;
    font-size: 1rem;
    color: #343a40;
    font-weight: 500;
  }

  .video-modal-date i, .video-modal-category i {
    color: #dc3545;
    margin-left: 10px;
    font-size: 1.2rem;
  }

  .date-label {
    margin-left: 5px;
    font-weight: 600;
  }

  .video-modal-description {
    line-height: 1.9;
    color: #212529;
    font-size: 1.15rem;
    text-align: justify;
    margin-bottom: 30px;
    padding: 0 5px;
    font-weight: 400;
    border-right: 3px solid #f8f9fa;
    padding-right: 15px;
    transition: border-color 0.3s ease;
  }

  .video-modal-description:hover {
    border-right-color: #dc3545;
  }

  .professional-video-container {
    position: relative;
    padding-bottom: 56.25%;
    height: 0;
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    transform: scale(1);
    background-color: #000;
  }

  .professional-video-container:hover {
    transform: scale(1.01);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
  }

  .video-iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0;
    opacity: 0;
    transition: opacity 0.5s ease;
  }

  .professional-video-container.loaded .video-iframe {
    opacity: 1;
  }

  .video-loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    z-index: 1;
    transition: opacity 0.5s ease;
  }

  .professional-video-container.loaded .video-loading-indicator {
    opacity: 0;
    pointer-events: none;
  }

  .video-modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1rem 1.5rem;
  }

  .video-close-btn {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
    font-weight: 600;
    padding: 0.5rem 1.25rem;
    border-radius: 30px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(108, 117, 125, 0.3);
  }

  .video-close-btn:hover {
    background-color: #5a6268;
    border-color: #545b62;
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(108, 117, 125, 0.4);
  }

  .video-close-btn:active {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(108, 117, 125, 0.3);
  }

  @media (max-width: 768px) {
    .video-modal-meta {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
      padding: 12px 15px;
    }

    .video-modal-description {
      font-size: 1rem;
      line-height: 1.7;
    }

    .professional-video-container {
      margin-top: 15px;
    }
  }
</style>
