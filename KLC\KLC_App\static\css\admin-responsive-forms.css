/* Admin Responsive Forms CSS
 * This file contains professional styling for all admin forms
 * with responsive design for both desktop and mobile devices
 */

/* Base form styling */
.professional-form {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.professional-form:hover {
    box-shadow: 0 12px 28px rgba(0, 0, 0, 0.12);
}

.professional-form .card-body {
    padding: 2rem;
}

/* Form header styling */
.form-header {
    margin-bottom: 2rem;
    text-align: center;
}

.icon-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(58, 124, 165, 0.1);
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.form-header h3 {
    font-weight: 700;
    color: #2c5d7c;
    margin-bottom: 0.5rem;
}

.form-header p {
    color: #6c757d;
    font-size: 1rem;
}

/* Form group styling */
.form-group {
    margin-bottom: 1.5rem;
    animation: fadeInUp 0.5s ease-out forwards;
    opacity: 0;
}

.form-group:nth-child(1) { animation-delay: 0.1s; }
.form-group:nth-child(2) { animation-delay: 0.2s; }
.form-group:nth-child(3) { animation-delay: 0.3s; }
.form-group:nth-child(4) { animation-delay: 0.4s; }
.form-group:nth-child(5) { animation-delay: 0.5s; }
.form-group:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form label styling */
.form-label {
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #2c5d7c;
    display: flex;
    align-items: center;
}

.form-label i {
    margin-left: 0.5rem;
    color: #3a7ca5;
}

/* Input group styling */
.input-group {
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.input-group-lg > .form-control,
.input-group-lg > .input-group-text,
.input-group-lg > .btn {
    padding: 0.75rem 1.25rem;
    font-size: 1rem;
}

.input-group-text {
    background-color: #fff;
    border-color: #e0e7ef;
    color: #3a7ca5;
}

.form-control, .form-select {
    border-color: #e0e7ef;
    color: #495057;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #3a7ca5;
    box-shadow: none;
}

.input-group-focus {
    box-shadow: 0 0 0 0.25rem rgba(58, 124, 165, 0.25);
}

/* Form text and validation */
.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

.invalid-feedback {
    font-size: 0.875rem;
    color: #dc3545;
}

/* Alert styling */
.alert {
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    border: none;
}

.bg-info-light {
    background-color: rgba(33, 150, 243, 0.1) !important;
}

.bg-primary-light {
    background-color: rgba(58, 124, 165, 0.1) !important;
}

/* Button styling */
.btn {
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-lg {
    padding: 0.875rem 2rem;
}

.btn-primary {
    background-color: #3a7ca5;
    border-color: #3a7ca5;
}

.btn-primary:hover {
    background-color: #2c5d7c;
    border-color: #2c5d7c;
}

.btn-outline-primary {
    color: #3a7ca5;
    border-color: #3a7ca5;
}

.btn-outline-primary:hover {
    background-color: #3a7ca5;
    color: white;
}

.btn-outline-secondary {
    color: #6c757d;
    border-color: #e0e7ef;
}

.btn-outline-secondary:hover {
    background-color: #f8f9fa;
    color: #495057;
    border-color: #e0e7ef;
}

/* Section header styling */
.section-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e0e7ef;
}

.section-icon-circle {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(58, 124, 165, 0.1);
    margin-right: 1rem;
}

.section-header h2 {
    color: #2c5d7c;
    font-weight: 600;
    margin: 0;
}

/* Calendar styling */
.flatpickr-calendar {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    font-family: 'Cairo', 'Tajawal', sans-serif;
    width: 320px !important;
    direction: rtl;
    background: #fff;
    z-index: 9999 !important;
    overflow: hidden;
}

.flatpickr-months {
    padding: 0.75rem;
    background-color: #3a7ca5;
    color: white;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

.flatpickr-month {
    height: 40px;
}

.flatpickr-current-month {
    font-size: 1.1rem;
    font-weight: 600;
    padding-top: 0;
}

.flatpickr-current-month .flatpickr-monthDropdown-months,
.numInputWrapper {
    font-weight: 600;
    color: white;
}

.flatpickr-current-month .flatpickr-monthDropdown-months option {
    color: #333;
}

.flatpickr-weekdays {
    background-color: #f8f9fa;
    margin-top: 0;
}

.flatpickr-weekday {
    font-weight: 600;
    color: #555;
    height: 36px;
    line-height: 36px;
    background-color: #f8f9fa;
}

.flatpickr-days {
    padding: 8px;
    border: none;
}

.dayContainer {
    width: 100%;
    min-width: 100%;
    max-width: 100%;
}

.flatpickr-day {
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
    height: 38px;
    line-height: 38px;
    margin: 2px;
    max-width: calc(100% / 7 - 4px);
    flex-basis: calc(100% / 7 - 4px);
    border: 1px solid transparent;
}

.flatpickr-day.selected {
    background-color: #3a7ca5;
    border-color: #3a7ca5;
    color: white;
}

.flatpickr-day.today {
    border-color: #3a7ca5;
    color: #3a7ca5;
    font-weight: bold;
}

.flatpickr-day:hover {
    background-color: #e9f7ef;
}

.flatpickr-prev-month,
.flatpickr-next-month {
    padding: 10px;
    fill: white !important;
}

.flatpickr-prev-month:hover,
.flatpickr-next-month:hover {
    background: rgba(255, 255, 255, 0.1);
}

.flatpickr-prev-month svg,
.flatpickr-next-month svg {
    fill: white !important;
}

/* Responsive styles */
@media (max-width: 991.98px) {
    .professional-form .card-body {
        padding: 1.5rem;
    }
    
    .col-lg-8.mx-auto {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

@media (max-width: 767.98px) {
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .section-header > div {
        display: flex;
        width: 100%;
    }
    
    .section-header .btn {
        width: 100%;
        margin-top: 0.5rem;
    }
    
    .professional-form .card-body {
        padding: 1.25rem;
    }
    
    .form-header h3 {
        font-size: 1.25rem;
    }
    
    .icon-circle {
        width: 60px;
        height: 60px;
    }
    
    .input-group-lg > .form-control,
    .input-group-lg > .input-group-text,
    .input-group-lg > .btn {
        padding: 0.625rem 1rem;
        font-size: 0.95rem;
    }
    
    .btn-lg {
        padding: 0.75rem 1.5rem;
    }
    
    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }
    
    .d-flex.justify-content-between .btn {
        width: 100%;
    }
    
    .flatpickr-calendar {
        width: 300px !important;
    }
}

@media (max-width: 575.98px) {
    .professional-form .card-body {
        padding: 1rem;
    }
    
    .form-label {
        font-size: 0.95rem;
    }
    
    .form-text {
        font-size: 0.8rem;
    }
    
    .alert {
        padding: 0.75rem;
    }
    
    .section-icon-circle {
        width: 40px;
        height: 40px;
    }
    
    .section-header h2 {
        font-size: 1.25rem;
    }
}

/* Dark mode support */
[data-theme="dark"] .professional-form {
    background-color: #2d3748;
}

[data-theme="dark"] .form-header h3 {
    color: #e2e8f0;
}

[data-theme="dark"] .form-header p,
[data-theme="dark"] .form-text {
    color: #a0aec0;
}

[data-theme="dark"] .form-label {
    color: #e2e8f0;
}

[data-theme="dark"] .input-group-text {
    background-color: #3a4a5e;
    border-color: #4a5568;
    color: #e2e8f0;
}

[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
    background-color: #3a4a5e;
    border-color: #4a5568;
    color: #e2e8f0;
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
    border-color: #63b3ed;
}

[data-theme="dark"] .input-group-focus {
    box-shadow: 0 0 0 0.25rem rgba(99, 179, 237, 0.25);
}

[data-theme="dark"] .btn-outline-secondary {
    color: #cbd5e0;
    border-color: #4a5568;
}

[data-theme="dark"] .btn-outline-secondary:hover {
    background-color: #4a5568;
    color: #e2e8f0;
    border-color: #4a5568;
}

[data-theme="dark"] .section-header {
    border-bottom-color: #4a5568;
}

[data-theme="dark"] .bg-info-light {
    background-color: rgba(56, 178, 255, 0.1) !important;
}

[data-theme="dark"] .bg-primary-light {
    background-color: rgba(99, 179, 237, 0.1) !important;
}

[data-theme="dark"] .flatpickr-calendar {
    background-color: #2d3748;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .flatpickr-weekdays {
    background-color: #3a4a5e;
}

[data-theme="dark"] .flatpickr-weekday {
    color: #e2e8f0;
    background-color: #3a4a5e;
}

[data-theme="dark"] .flatpickr-day {
    color: #e2e8f0;
}

[data-theme="dark"] .flatpickr-day:hover {
    background-color: #4a5568;
}

[data-theme="dark"] .flatpickr-day.today {
    border-color: #63b3ed;
    color: #63b3ed;
}

[data-theme="dark"] .flatpickr-day.selected {
    background-color: #63b3ed;
    border-color: #63b3ed;
}

/* RTL specific adjustments */
html[dir="rtl"] .section-icon-circle {
    margin-right: 0;
    margin-left: 1rem;
}

html[dir="rtl"] .form-label i {
    margin-left: 0.5rem;
    margin-right: 0;
}

html[dir="rtl"] .input-group > .form-control:not(:last-child),
html[dir="rtl"] .input-group > .form-select:not(:last-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

html[dir="rtl"] .input-group > .form-control:not(:first-child),
html[dir="rtl"] .input-group > .form-select:not(:first-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}