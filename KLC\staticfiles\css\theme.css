:root {
    /* Light theme (default) */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --border-color: #dee2e6;
    --card-bg: #ffffff;
    --navbar-bg: #343a40;
    --navbar-text: #ffffff;
    --input-bg: #ffffff;
    --input-border: #ced4da;
    --table-bg: #ffffff;
    --table-border: #dee2e6;
    --hover-bg: #f8f9fa;
    --shadow-color: rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] {
    --bg-primary: #1a1d20;
    --bg-secondary: #2c3034;
    --text-primary: #e9ecef;
    --text-secondary: #adb5bd;
    --border-color: #495057;
    --card-bg: #2c3034;
    --navbar-bg: #212529;
    --navbar-text: #ffffff;
    --input-bg: #2c3034;
    --input-border: #495057;
    --table-bg: #2c3034;
    --table-border: #495057;
    --hover-bg: #343a40;
    --shadow-color: rgba(0, 0, 0, 0.3);
}

/* Apply theme variables */
body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.navbar {
    background-color: var(--navbar-bg) !important;
    transition: all 0.3s ease;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
}

/* Navbar scrolled state */
.navbar-scrolled {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    background-color: rgba(33, 37, 41, 0.95) !important;
    backdrop-filter: blur(10px);
}

.navbar-dark .navbar-nav .nav-link {
    color: var(--navbar-text);
    transition: all 0.3s ease;
    position: relative;
}

.navbar-dark .navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.navbar-dark .navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.15);
    font-weight: bold;
}

/* Add a subtle underline animation on hover */
.navbar-dark .navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background-color: var(--navbar-text);
    transition: all 0.3s ease;
    transform: translateX(-50%);
    opacity: 0;
}

.navbar-dark .navbar-nav .nav-link:hover::after {
    width: 70%;
    opacity: 1;
}

.card {
    background-color: var(--card-bg);
    border-color: var(--border-color);
}

.table {
    color: var(--text-primary);
    background-color: var(--table-bg);
}

.table td, .table th {
    border-color: var(--table-border);
}

.form-control {
    background-color: var(--input-bg);
    border-color: var(--input-border);
    color: var(--text-primary);
}

.form-control:focus {
    background-color: var(--input-bg);
    color: var(--text-primary);
}

.modal-content {
    background-color: var(--card-bg);
    color: var(--text-primary);
}

.modal-header {
    border-bottom-color: var(--border-color);
}

.modal-footer {
    border-top-color: var(--border-color);
}

/* Theme toggle button styles */
.theme-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    background-color: var(--card-bg);
    border: 2px solid var(--border-color);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 10px var(--shadow-color);
    transition: all 0.3s ease;
}

.theme-toggle:hover {
    transform: scale(1.1);
}

.theme-toggle i {
    font-size: 1.5rem;
    color: var(--text-primary);
}

/* Dark mode specific adjustments */
[data-theme="dark"] .text-dark {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .bg-light {
    background-color: var(--bg-secondary) !important;
}

[data-theme="dark"] .border-light {
    border-color: var(--border-color) !important;
}

/* Admin Dashboard Dark Mode Specific Styles */
[data-theme="dark"] .card-subtitle {
    color: var(--text-secondary) !important;
}

[data-theme="dark"] .card-title {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .section-header h2 {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .table {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .table thead th {
    color: var(--text-secondary) !important;
    border-bottom-color: var(--border-color) !important;
}

[data-theme="dark"] .table tbody td {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .badge {
    color: #ffffff !important;
}

[data-theme="dark"] .form-label {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .form-text {
    color: var(--text-secondary) !important;
}

[data-theme="dark"] .modal-title {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .user-info span {
    color: var(--text-secondary) !important;
}

[data-theme="dark"] .user-info h4 {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .text-danger {
    color: #ff6b6b !important;
}

[data-theme="dark"] .text-success {
    color: #51cf66 !important;
}

[data-theme="dark"] .text-warning {
    color: #ffd43b !important;
}

[data-theme="dark"] .text-info {
    color: #4dabf7 !important;
}

[data-theme="dark"] .text-muted {
    color: var(--text-secondary) !important;
}

[data-theme="dark"] .alert-info {
    background-color: rgba(77, 171, 247, 0.1) !important;
    border-color: rgba(77, 171, 247, 0.2) !important;
    color: #4dabf7 !important;
}

[data-theme="dark"] .alert-success {
    background-color: rgba(81, 207, 102, 0.1) !important;
    border-color: rgba(81, 207, 102, 0.2) !important;
    color: #51cf66 !important;
}

[data-theme="dark"] .alert-warning {
    background-color: rgba(255, 212, 59, 0.1) !important;
    border-color: rgba(255, 212, 59, 0.2) !important;
    color: #ffd43b !important;
}

[data-theme="dark"] .alert-danger {
    background-color: rgba(255, 107, 107, 0.1) !important;
    border-color: rgba(255, 107, 107, 0.2) !important;
    color: #ff6b6b !important;
}

[data-theme="dark"] .sidebar {
    background-color: var(--bg-secondary) !important;
    border-right-color: var(--border-color) !important;
}

[data-theme="dark"] .sidebar .nav-link {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .sidebar .nav-link:hover {
    background-color: var(--hover-bg) !important;
}

[data-theme="dark"] .sidebar .nav-link.active {
    background-color: var(--hover-bg) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .navbar-brand {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .main-content {
    background-color: var(--bg-primary) !important;
}

/* Loading animation adjustments */
.loading-animation {
    background: rgba(0, 0, 0, 0.8) !important;
}

/* Alert adjustments */
.alert {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-primary);
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--text-secondary);
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-primary);
}

/* Header text shadow effect */
.text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Dark mode text shadow */
[data-theme="dark"] .text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}