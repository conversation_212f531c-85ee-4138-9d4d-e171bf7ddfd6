/**
 * Smooth scrolling for anchor links
 * This function makes the page scroll smoothly to the target section when an anchor link is clicked.
 * It prevents the default behavior of jumping to the section and instead animates the scroll.
 * @returns {void}
 */
function makeAnchorLinksScrollSmooth() {
  document.querySelectorAll('a.nav-link[href^="#"]').forEach((anchor) => {
    anchor.addEventListener("click", function (e) {
      e.preventDefault();
      document.querySelector(this.getAttribute("href")).scrollIntoView({
        behavior: "smooth",
      });
    });
  });
}

/**
 * Prevent the browser back button from navigating back
 * Instead, just refresh the page.
 */
function makeBackButtonRefreshOnly() {
  history.pushState(null, "", location.href);
  window.addEventListener("popstate", function () {
    location.reload();
  });
}

/**
 * Set a background image on the page and apply a dark overlay
 * @param {string} path - Path to the background image
 */
function addBackgroundImage(path) {
  document.body.style.backgroundImage = `url(${path})`;
  document.body.style.backgroundSize = "cover";
  document.body.style.backgroundPosition = "center";
  document.body.style.backgroundRepeat = "no-repeat";
  addOverlay();
}

/**
 * Add a semi-transparent overlay for better text visibility
 */
function addOverlay() {
  const overlay = document.createElement("div");
  overlay.style.position = "fixed";
  overlay.style.top = "0";
  overlay.style.left = "0";
  overlay.style.width = "100%";
  overlay.style.height = "100%";
  overlay.style.background =
    "linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5))";
  overlay.style.zIndex = "-1";
  document.body.appendChild(overlay);
}

/**
 * Handle AJAX submission of the Hall Reservation form
 */
function setupHallReservationForm() {
  const form = document.querySelector(
    "form[action='{% url 'reserve_hall' %}']"
  );
  if (!form) return;

  form.addEventListener("submit", function (event) {
    event.preventDefault();
    const formData = new FormData(form);
    fetch(form.action, {
      method: form.method,
      body: formData,
      headers: {
        "X-CSRFToken": form.querySelector("[name=csrfmiddlewaretoken]").value,
      },
    }).then((response) => response.text());
  });
}

/**
 * Handle AJAX submission of the transaction form
 */
function setupTransactionsForm() {
  const form = document.querySelector(
    "form[action='{% url 'make_transaction' %}']"
  );
  if (!form) return;

  form.addEventListener("submit", function (event) {
    event.preventDefault();
    const formData = new FormData(form);
    fetch(form.action, {
      method: form.method,
      body: formData,
      headers: {
        "X-CSRFToken": form.querySelector("[name=csrfmiddlewaretoken]").value,
      },
    }).then((response) => response.text());
  });
}

// Main initializer
document.addEventListener("DOMContentLoaded", function () {
  makeAnchorLinksScrollSmooth();
  makeBackButtonRefreshOnly();
  addBackgroundImage("/static/images/services-background.jpg");
  setupHallReservationForm();
  setupTransactionsForm();
  ///// Note: JS for the transaction modal form is in the transaction_modal.html file //////////
  /////// Note: JS for Taboo redirection is in the services.html file //////////
});
