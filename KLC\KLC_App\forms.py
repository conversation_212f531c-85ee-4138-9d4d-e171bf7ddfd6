# KLC_APP/forms.py
from django import forms
from django.forms.widgets import Input

# Custom widget for multiple file uploads
class MultipleFileInput(Input):
    input_type = 'file'
    needs_multipart_form = True

    def __init__(self, attrs=None):
        if attrs is None:
            attrs = {}
        attrs['multiple'] = True
        super().__init__(attrs)

    def value_from_datadict(self, data, files, name):
        """
        Get list of files from uploaded data.
        """
        if hasattr(files, 'getlist'):
            return files.getlist(name)
        else:
            return files.get(name)

# Custom field for multiple files
class MultipleFileField(forms.FileField):
    widget = MultipleFileInput

    def clean(self, data, initial=None):
        # Handle case when no file is submitted
        if data is None or data == '':
            if not self.required:
                return []
            # Let the default required validation raise an error
            return super().clean(data, initial)

        # Handle case when multiple files are submitted
        if isinstance(data, (list, tuple)):
            result = []
            for d in data:
                if d is not None and d != '':
                    # Use the parent class's clean method for each file
                    file_obj = forms.FileField.clean(self, d, initial)
                    result.append(file_obj)
            return result

        # Handle case when a single file is submitted
        # This is the key part - request.FILES gives us a UploadedFile object
        # which needs to be wrapped in a list
        if data is not None and data != '':
            file_obj = forms.FileField.clean(self, data, initial)
            return [file_obj]
        return []

class UploadFileForm(forms.Form):
    file = forms.FileField(
        label='Excel File',
        widget=forms.FileInput(attrs={
            'accept': '.xlsx, .xls',
            'style': 'padding: 0.5rem; width: 250px;'
        })
    )


class EditUserForm(forms.Form):
    id_number = forms.CharField(
        label='رقم الهوية',
        max_length=20,
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    full_name = forms.CharField(
        label='الاسم الكامل',
        max_length=100,
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    owed_amount = forms.DecimalField(
        label='المبلغ المستحق (ر.س)',
        max_digits=10,
        decimal_places=2,
        widget=forms.NumberInput(attrs={'class': 'form-control'})
    )

class AddUserForm(forms.Form):
    national_id = forms.CharField(label='رقم الهوية الوطنية')
    name = forms.CharField(label='الاسم الثلاثي')
    debts_amount_2024 = forms.DecimalField(label='المديونية لعام 2024')

class NewsForm(forms.Form):
    title = forms.CharField(
        label='عنوان الخبر',
        max_length=255,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'أدخل عنوان الخبر'})
    )
    description = forms.CharField(
        label='وصف الخبر',
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 4, 'placeholder': 'أدخل وصف الخبر'})
    )
    image = forms.ImageField(
        label='الصورة الرئيسية',
        required=False,
        widget=forms.FileInput(attrs={'class': 'form-control', 'accept': 'image/*'})
    )
    additional_images = MultipleFileField(
        label='صور إضافية (اختياري)',
        required=False,
        widget=MultipleFileInput(attrs={
            'class': 'form-control',
            'accept': 'image/*'
        }),
        help_text='يمكنك اختيار عدة صور في نفس الوقت'
    )
    video_url = forms.URLField(
        label='رابط الفيديو (اختياري)',
        required=False,
        widget=forms.URLInput(attrs={'class': 'form-control', 'placeholder': 'أدخل رابط الفيديو (Google Drive)'})
    )
    news_type = forms.CharField(
        label='نوع الخبر',
        max_length=100,
        initial='أخبار',
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'أخبار، إنجازات، مشاريع، إلخ'})
    )
    is_featured = forms.BooleanField(
        label='عرض كخبر رئيسي',
        required=False,
        initial=False,
        help_text='تحديد هذا الخيار سيجعل هذا الخبر يظهر كخبر رئيسي (كبير) في الصفحة الرئيسية',
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )