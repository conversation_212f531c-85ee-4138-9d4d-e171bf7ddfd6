import time
import random

def static_version(request):
    """
    Add a version parameter to static files to prevent caching.
    This will be accessible in templates as {{ STATIC_VERSION }}
    """
    # Generate a version based on the current timestamp
    version = int(time.time())
    # Add some randomness to ensure uniqueness
    version = f"{version}-{random.randint(1000, 9999)}"
    
    return {
        'STATIC_VERSION': version
    }
