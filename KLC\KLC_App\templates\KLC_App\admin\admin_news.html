{% extends 'KLC_App/admin/admin_base.html' %}

{% block title %}إدارة الأخبار - لوحة التحكم{% endblock %}

{% block extra_css %}
<style>
    /* Featured badge styling */
    .featured-badge {
        display: inline-block;
    }

    .featured-badge .badge {
        font-size: 0.85rem;
        padding: 0.5rem 0.75rem;
        font-weight: 600;
        border-radius: 30px;
        box-shadow: 0 2px 5px rgba(255, 193, 7, 0.3);
        animation: pulse-glow 2s infinite;
    }

    @keyframes pulse-glow {
        0% {
            box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.5);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
        }
    }

    /* Row highlighting for featured news */
    tr.featured-news-row {
        background-color: rgba(255, 243, 205, 0.3) !important;
    }

    [data-theme="dark"] tr.featured-news-row {
        background-color: rgba(255, 193, 7, 0.1) !important;
    }

    /* Star icon in the title */
    .featured-star {
        color: #ffc107;
        margin-right: 0.5rem;
        animation: star-rotate 4s linear infinite;
    }

    @keyframes star-rotate {
        0% {
            transform: rotate(0deg);
        }
        25% {
            transform: rotate(10deg);
        }
        50% {
            transform: rotate(0deg);
        }
        75% {
            transform: rotate(-10deg);
        }
        100% {
            transform: rotate(0deg);
        }
    }
</style>
{% endblock %}

{% block body_class %}admin-news{% endblock %}

{% block content %}
<div class="section-header">
    <h2><i class="fas fa-newspaper me-2"></i> إدارة الأخبار والإنجازات</h2>
    <div class="d-flex align-items-center">
        <div class="featured-news-info me-3">
            <div class="d-flex align-items-center p-2 rounded-pill bg-light">
                <div class="featured-icon-small me-2">
                    <i class="fas fa-star"></i>
                </div>
                <span class="featured-text">الخبر الرئيسي يظهر بشكل بارز في الصفحة الرئيسية</span>
            </div>
        </div>
        <a href="{% url 'add_news' %}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-2"></i> إضافة خبر جديد
        </a>
    </div>
</div>

<style>
    .featured-news-info {
        display: flex;
        align-items: center;
    }

    .featured-news-info .featured-icon-small {
        color: #ffc107;
        font-size: 1rem;
    }

    .featured-news-info .featured-text {
        font-size: 0.9rem;
        color: #6c757d;
        font-weight: 500;
    }

    [data-theme="dark"] .featured-news-info .bg-light {
        background-color: rgba(255, 255, 255, 0.1) !important;
    }

    [data-theme="dark"] .featured-news-info .featured-text {
        color: #e2e8f0;
    }
</style>

{% if messages %}
<div class="alert-container mb-4">
    {% for message in messages %}
    <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
        {{ message }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endfor %}
</div>
{% endif %}

<div class="card shadow-sm mb-4">
    <div class="card-body">
        <div class="table-responsive">
            <table id="newsTable" class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>العنوان</th>
                        <th>النوع</th>
                        <th>تاريخ النشر</th>
                        <th>الصورة</th>
                        <th>خبر رئيسي</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for news in news_list %}
                    <tr {% if news.is_featured %}class="featured-news-row"{% endif %}>
                        <td>
                            {% if news.is_featured %}
                                <i class="fas fa-star featured-star" title="خبر رئيسي"></i>
                            {% endif %}
                            {{ news.title }}
                        </td>
                        <td>{{ news.type|default:"أخبار" }}</td>
                        <td>{{ news.published_at }}</td>
                        <td>
                            {% if news.image_src %}
                            <img src="{{ news.image_src }}" alt="{{ news.title }}" class="img-thumbnail" style="max-width: 100px; max-height: 60px;">
                            {% else %}
                            <span class="text-muted">لا توجد صورة</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if news.is_featured %}
                                <div class="featured-badge">
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-star me-1"></i> خبر رئيسي
                                    </span>
                                </div>
                            {% else %}
                                <span class="badge bg-light text-dark">عادي</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{% url 'edit_news' news.id %}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteNewsModal" data-news-id="{{ news.id }}" data-news-title="{{ news.title }}">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="text-center">لا توجد أخبار مضافة حتى الآن</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Delete News Modal -->
<div class="modal fade" id="deleteNewsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title"><i class="fas fa-exclamation-triangle me-2"></i> تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الخبر: <strong id="newsTitle"></strong>؟</p>
                <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <a href="#" id="confirmDeleteBtn" class="btn btn-danger">تأكيد الحذف</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initialize DataTable
        $('#newsTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
            },
            "order": [[2, "desc"]], // Sort by date (3rd column) descending
            "responsive": true,
            "dom": 'Bfrtip',
            "buttons": [
                'copy', 'excel', 'pdf', 'print'
            ]
        });

        // Handle delete modal
        $('#deleteNewsModal').on('show.bs.modal', function (event) {
            const button = $(event.relatedTarget);
            const newsId = button.data('news-id');
            const newsTitle = button.data('news-title');

            $('#newsTitle').text(newsTitle);
            $('#confirmDeleteBtn').attr('href', `/admin/delete-news/${newsId}/`);
        });
    });
</script>
{% endblock %}
