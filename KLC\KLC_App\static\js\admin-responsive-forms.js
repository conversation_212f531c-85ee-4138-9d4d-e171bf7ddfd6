/**
 * Admin Responsive Forms JavaScript
 * This file contains common functionality for all admin forms
 * with responsive design for both desktop and mobile devices
 */

document.addEventListener('DOMContentLoaded', function() {
    // Add focus effects for professional look
    const formInputs = document.querySelectorAll('.form-control, .form-select');
    formInputs.forEach(input => {
        // Add focus effect
        input.addEventListener('focus', function() {
            const inputGroup = this.closest('.input-group');
            if (inputGroup) {
                inputGroup.classList.add('input-group-focus');
            }
        });

        // Remove focus effect
        input.addEventListener('blur', function() {
            const inputGroup = this.closest('.input-group');
            if (inputGroup) {
                inputGroup.classList.remove('input-group-focus');
            }
        });
    });

    // Add validation for required fields
    const forms = document.querySelectorAll('form.admin-form');
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            let isValid = true;
            const requiredInputs = form.querySelectorAll('[required]');
            
            requiredInputs.forEach(input => {
                if (!input.value.trim()) {
                    isValid = false;
                    event.preventDefault();
                    
                    // Add validation styling
                    input.classList.add('is-invalid');
                    
                    // Create or update validation message
                    let feedbackElement = input.nextElementSibling;
                    if (!feedbackElement || !feedbackElement.classList.contains('invalid-feedback')) {
                        feedbackElement = document.createElement('div');
                        feedbackElement.classList.add('invalid-feedback', 'd-block', 'mt-2');
                        input.parentNode.insertBefore(feedbackElement, input.nextSibling);
                    }
                    
                    // Set validation message
                    const fieldName = input.getAttribute('placeholder') || 
                                     input.getAttribute('name') || 
                                     'هذا الحقل';
                    feedbackElement.innerHTML = `<i class="fas fa-exclamation-circle me-1"></i>يرجى تعبئة ${fieldName}`;
                }
            });
            
            // Scroll to first invalid input
            if (!isValid) {
                const firstInvalid = form.querySelector('.is-invalid');
                if (firstInvalid) {
                    firstInvalid.focus();
                    firstInvalid.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                }
            }
        });
        
        // Clear validation styling on input
        form.querySelectorAll('input, select, textarea').forEach(input => {
            input.addEventListener('input', function() {
                if (this.classList.contains('is-invalid')) {
                    this.classList.remove('is-invalid');
                    
                    // Remove validation message if exists
                    const feedbackElement = this.nextElementSibling;
                    if (feedbackElement && feedbackElement.classList.contains('invalid-feedback')) {
                        feedbackElement.style.display = 'none';
                    }
                }
            });
        });
    });

    // Responsive adjustments for mobile
    function handleResponsiveLayout() {
        const isMobile = window.innerWidth < 768;
        
        // Adjust button groups on mobile
        const buttonGroups = document.querySelectorAll('.d-flex.justify-content-between');
        buttonGroups.forEach(group => {
            if (isMobile) {
                group.classList.add('flex-column');
                group.querySelectorAll('.btn').forEach(btn => {
                    btn.classList.add('w-100', 'mb-2');
                });
            } else {
                group.classList.remove('flex-column');
                group.querySelectorAll('.btn').forEach(btn => {
                    btn.classList.remove('w-100', 'mb-2');
                });
            }
        });
    }

    // Initial call and window resize listener
    handleResponsiveLayout();
    window.addEventListener('resize', handleResponsiveLayout);

    // Add animation to form elements
    const formGroups = document.querySelectorAll('.form-group');
    formGroups.forEach((group, index) => {
        group.style.animationDelay = `${0.1 * (index + 1)}s`;
    });
});