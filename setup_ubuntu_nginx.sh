#!/bin/bash

# Exit on error
set -e

# Variables - MODIFY THESE
SERVER_IP="***********"  # Your server IP address
PROJECT_PATH="/home/<USER>/KLC_django"  # Path to your Django project
DJANGO_PORT=8000  # Port your Django app runs on
USE_HTTPS=true  # Set to true to set up HTTPS

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Print section header
section() {
    echo -e "\n${GREEN}==== $1 ====${NC}\n"
}

# Print info message
info() {
    echo -e "${YELLOW}INFO: $1${NC}"
}

# Print error message
error() {
    echo -e "${RED}ERROR: $1${NC}"
    exit 1
}

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    error "Please run as root (use sudo)"
fi

section "Updating System Packages"
apt update
apt upgrade -y

section "Installing Required Packages"
apt install -y python3-pip python3-venv nginx certbot python3-certbot-nginx

section "Setting Up Python Environment"
if [ ! -d "$PROJECT_PATH" ]; then
    error "Project directory $PROJECT_PATH does not exist"
fi

cd "$PROJECT_PATH"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    info "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment and install requirements
info "Installing requirements..."
source venv/bin/activate
pip install -r requirements.txt
pip install gunicorn

section "Configuring Django Settings"
# Update Django settings for production
if [ -f "KLC/KLC/settings.py" ]; then
    info "Updating Django settings..."
    # Backup settings file
    cp KLC/KLC/settings.py KLC/KLC/settings.py.bak
    
    # Update settings for HTTPS
    if [ "$USE_HTTPS" = true ]; then
        sed -i "s/SECURE_SSL_REDIRECT = False/SECURE_SSL_REDIRECT = True/" KLC/KLC/settings.py
        sed -i "s/SESSION_COOKIE_SECURE = False/SESSION_COOKIE_SECURE = True/" KLC/KLC/settings.py
        sed -i "s/CSRF_COOKIE_SECURE = False/CSRF_COOKIE_SECURE = True/" KLC/KLC/settings.py
    fi
    
    # Update allowed hosts
    sed -i "s/ALLOWED_HOSTS = \[.*\]/ALLOWED_HOSTS = ['$SERVER_IP', 'localhost', '127.0.0.1']/" KLC/KLC/settings.py
fi

section "Collecting Static Files"
python manage.py collectstatic --noinput

section "Setting Up Nginx"
# Create Nginx configuration
cat > /etc/nginx/sites-available/klc << EOF
server {
    listen 80;
    server_name $SERVER_IP;
    
    $(if [ "$USE_HTTPS" = true ]; then echo "    return 301 https://\$host\$request_uri;"; fi)
    
    $(if [ "$USE_HTTPS" = false ]; then
    echo "    location /static/ {
        alias $PROJECT_PATH/staticfiles/;
        expires 30d;
        add_header Cache-Control \"public, max-age=2592000\";
    }

    location /media/ {
        alias $PROJECT_PATH/media/;
        expires 30d;
        add_header Cache-Control \"public, max-age=2592000\";
    }

    location / {
        proxy_pass http://127.0.0.1:$DJANGO_PORT;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # Additional security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection \"1; mode=block\";"
    fi)
}

$(if [ "$USE_HTTPS" = true ]; then
echo "server {
    listen 443 ssl;
    server_name $SERVER_IP;

    # SSL certificates will be configured later
    ssl_certificate /etc/ssl/certs/klc-selfsigned.crt;
    ssl_certificate_key /etc/ssl/private/klc-selfsigned.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;

    location /static/ {
        alias $PROJECT_PATH/staticfiles/;
        expires 30d;
        add_header Cache-Control \"public, max-age=2592000\";
    }

    location /media/ {
        alias $PROJECT_PATH/media/;
        expires 30d;
        add_header Cache-Control \"public, max-age=2592000\";
    }

    location / {
        proxy_pass http://127.0.0.1:$DJANGO_PORT;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # Additional security headers
    add_header Strict-Transport-Security \"max-age=31536000; includeSubDomains\" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection \"1; mode=block\";
}"
fi)
EOF

# Enable the site
ln -sf /etc/nginx/sites-available/klc /etc/nginx/sites-enabled/

# Remove default site if it exists
if [ -f /etc/nginx/sites-enabled/default ]; then
    rm /etc/nginx/sites-enabled/default
fi

# Test Nginx configuration
nginx -t

# Set up SSL if requested
if [ "$USE_HTTPS" = true ]; then
    section "Setting Up SSL"
    info "Generating self-signed SSL certificates..."
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout /etc/ssl/private/klc-selfsigned.key \
        -out /etc/ssl/certs/klc-selfsigned.crt \
        -subj "/CN=$SERVER_IP"
    
    info "SSL certificates generated successfully"
fi

section "Setting Up Systemd Service"
# Create systemd service file
cat > /etc/systemd/system/klc-django.service << EOF
[Unit]
Description=KLC Django Application
After=network.target

[Service]
User=ubuntu
Group=ubuntu
WorkingDirectory=$PROJECT_PATH
ExecStart=$PROJECT_PATH/venv/bin/gunicorn --workers 3 --bind 127.0.0.1:$DJANGO_PORT KLC.wsgi:application
Restart=on-failure

[Install]
WantedBy=multi-user.target
EOF

# Reload systemd, enable and start the service
systemctl daemon-reload
systemctl enable klc-django.service
systemctl start klc-django.service

section "Configuring Firewall"
# Configure firewall if ufw is installed
if command -v ufw &> /dev/null; then
    info "Configuring firewall..."
    ufw allow 80
    if [ "$USE_HTTPS" = true ]; then
        ufw allow 443
    fi
    
    # Check if firewall is active, if not, enable it
    if ! ufw status | grep -q "Status: active"; then
        info "Enabling firewall..."
        ufw --force enable
    fi
fi

section "Restarting Nginx"
systemctl restart nginx

section "Setup Complete"
echo -e "${GREEN}Setup completed successfully!${NC}"
echo -e "Your Django application should now be running at:"
if [ "$USE_HTTPS" = true ]; then
    echo -e "${GREEN}https://$SERVER_IP${NC}"
else
    echo -e "${GREEN}http://$SERVER_IP${NC}"
fi

echo -e "\nTo check the status of your Django application, run:"
echo -e "${YELLOW}sudo systemctl status klc-django.service${NC}"

echo -e "\nTo view the logs of your Django application, run:"
echo -e "${YELLOW}sudo journalctl -u klc-django.service${NC}"

echo -e "\nTo restart your Django application, run:"
echo -e "${YELLOW}sudo systemctl restart klc-django.service${NC}"

echo -e "\nTo restart Nginx, run:"
echo -e "${YELLOW}sudo systemctl restart nginx${NC}"
