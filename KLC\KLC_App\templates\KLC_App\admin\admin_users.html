{% extends 'KLC_App/admin/admin_base.html' %}

{% block title %}إدارة المستخدمين - لوحة التحكم{% endblock %}

{% block body_class %}admin-users{% endblock %}

{% block content %}
<div class="section-header">
    <h2><i class="fas fa-users-cog me-2"></i> إدارة المستخدمين</h2>
    <div>
        <a href="{% url 'add_user' %}" class="btn btn-primary me-2">
            <i class="fas fa-user-plus me-2"></i> إضافة مستخدم جديد
        </a>
        <a href="{% url 'export_data' %}" class="btn btn-info">
            <i class="fas fa-file-export me-2"></i> تصدير البيانات
        </a>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title">
            <i class="fas fa-users me-2"></i> قائمة المستخدمين
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table align-middle" id="usersTable">
                <thead>
                    <tr>
                        <th scope="col">#</th>
                        <th scope="col">رقم الهوية</th>
                        <th scope="col">الاسم</th>
                        <th scope="col">المبلغ المستحق</th>
                        <th scope="col">الحالة</th>
                        <th scope="col">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ user.national_id }}</td>
                        <td>{{ user.name }}</td>
                        <td>{{ user.debt }}</td>
                        <td>
                            {% if user.debt > '1' %}
                                <span class="badge bg-danger">مدين</span>
                            {% else %}
                                <span class="badge bg-success">غير مدين</span>
                            {% endif %}
                        </td>
                        <td class="action-buttons">
                            <a href="{% url 'edit_user' user.id %}" class="btn btn-sm btn-warning" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{% url 'settle_debt' user.id %}" class="btn btn-sm btn-success" title="تسديد دين">
                                <i class="fas fa-money-bill-wave"></i>
                            </a>
                            <form method="POST" action="{% url 'delete_user' user.id %}" style="display: inline;">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-sm btn-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذا المستخدم؟')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="text-center">لا يوجد مستخدمين</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function () {
        $('#usersTable').DataTable({
            language: {
                url: "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
            },
            responsive: true,
            dom: '<"dt-controls"<"row"<"col-md-6"l><"col-md-6"f>>>' +
                 '<"row"<"col-sm-12"tr>>' +
                 '<"row mt-3"<"col-sm-5"i><"col-sm-7"p>>',
            pageLength: 10,
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]],
            buttons: [
                {
                    extend: 'copyHtml5',
                    text: '<i class="fas fa-copy"></i> نسخ',
                    className: 'btn btn-secondary btn-sm'
                },
                {
                    extend: 'excelHtml5',
                    text: '<i class="fas fa-file-excel"></i> تصدير Excel',
                    className: 'btn btn-success btn-sm'
                },
                {
                    extend: 'print',
                    text: '<i class="fas fa-print"></i> طباعة',
                    className: 'btn btn-primary btn-sm'
                }
            ],
            initComplete: function () {
                $('#usersTable_wrapper .dt-buttons').addClass('mb-3');

                // Add placeholder to search input
                $('.dataTables_filter input').attr('placeholder', 'البحث...');
            }
        });
    });
</script>
{% endblock %}
