{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خطأ في الخادم - مجلس قروي كفرعين</title>
    <!-- Force light mode by default -->
    <script>
        // Set light mode immediately before any other scripts run
        document.documentElement.setAttribute("data-theme", "light");
        localStorage.setItem("theme", "light");
    </script>
    <!-- Bootstrap 5.3 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="{% static 'css/index.css' %}" rel="stylesheet" />
    <link rel="icon" type="image/x-icon" href="{% static 'images/logo.png' %}" />
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            text-align: center;
            padding: 20px;
            position: relative;
        }

        .error-container {
            background: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(10px);
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 600px;
            width: 90%;
            z-index: 10;
        }

        [data-theme="dark"] .error-container {
            background: rgba(30, 41, 59, 0.85);
            color: #fff;
            border: 1px solid rgba(255, 255, 255, 0.05);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .error-code {
            font-size: 72px;
            font-weight: 700;
            color: #dc3545;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        [data-theme="dark"] .error-code {
            color: #f87171;
        }

        h1 {
            font-size: 32px;
            color: #2c3e50;
            margin-bottom: 20px;
        }

        [data-theme="dark"] h1 {
            color: #e2e8f0;
        }

        p {
            font-size: 18px;
            color: #6c757d;
            margin-bottom: 30px;
        }

        [data-theme="dark"] p {
            color: #cbd5e1;
        }

        .btn-primary {
            padding: 12px 30px;
            background-color: #3498db;
            color: #fff;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
            margin: 0 10px;
        }

        .btn-primary:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .btn-secondary {
            padding: 12px 30px;
            background-color: #dc3545;
            color: #fff;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
            margin: 0 10px;
        }

        .btn-secondary:hover {
            background-color: #c82333;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .logo {
            max-width: 120px;
            margin-bottom: 20px;
        }

        .theme-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            background: none;
            border: none;
            color: #333;
            font-size: 24px;
            cursor: pointer;
            z-index: 100;
        }

        [data-theme="dark"] .theme-toggle {
            color: #fff;
        }
    </style>
</head>
<body>
    <button class="theme-toggle" id="theme-toggle">
        <i class="fas fa-moon" id="dark-icon"></i>
        <i class="fas fa-sun" id="light-icon" style="display: none;"></i>
    </button>

    <div class="error-container">
        <img src="{% static 'images/logo.png' %}" alt="Kafr Ein Local Council Logo" class="logo">
        <div class="error-code">500</div>
        <h1>عفواً، حدث خطأ في الخادم</h1>
        <p>نواجه مشكلة فنية في الخادم. يرجى المحاولة مرة أخرى لاحقاً.</p>
        <div class="d-flex justify-content-center">
            <a href="/index" class="btn-primary">العودة للرئيسية</a>
            <button onclick="history.back()" class="btn-secondary">العودة للصفحة السابقة</button>
        </div>
    </div>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Set background image and overlay
        document.addEventListener('DOMContentLoaded', function() {
            // Set background image
            document.body.style.backgroundImage = "url('/static/images/services-background.jpg')";
            document.body.style.backgroundSize = "cover";
            document.body.style.backgroundPosition = "center";
            document.body.style.backgroundRepeat = "no-repeat";

            // Add overlay
            const overlay = document.createElement("div");
            overlay.style.position = "fixed";
            overlay.style.top = "0";
            overlay.style.left = "0";
            overlay.style.width = "100%";
            overlay.style.height = "100%";
            overlay.style.background = "linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5))";
            overlay.style.zIndex = "-1";
            document.body.appendChild(overlay);

            // Theme toggle functionality
            const themeToggle = document.getElementById('theme-toggle');
            const darkIcon = document.getElementById('dark-icon');
            const lightIcon = document.getElementById('light-icon');

            // Check for saved theme preference or use default
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);

            // Update icon display based on current theme
            if (savedTheme === 'dark') {
                darkIcon.style.display = 'none';
                lightIcon.style.display = 'inline';
            }

            // Toggle theme on click
            themeToggle.addEventListener('click', () => {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'light' ? 'dark' : 'light';

                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);

                // Toggle icon display
                if (newTheme === 'dark') {
                    darkIcon.style.display = 'none';
                    lightIcon.style.display = 'inline';
                } else {
                    darkIcon.style.display = 'inline';
                    lightIcon.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
