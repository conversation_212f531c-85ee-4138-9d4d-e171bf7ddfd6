document.addEventListener("DOMContentLoaded", () => {
  ///Add a background image to the page
  addBackgroundImage("/static/images/welcome-background.jpg");
  // Set the body styles
  setBodyStyles();
  const audio = document.getElementById("background-audio");
  const button = document.getElementById("start-audio-button");

  // Function to play the audio
  function playAudio() {
    audio.play();
  }

  // Function to pause the audio
  function pauseAudio() {
    audio.pause();
  }

  // Function to create and display the loading animation
  function showLoadingAnimation() {
    const loadingAnimation = document.createElement("div");
    loadingAnimation.className = "loading-animation";
    loadingAnimation.innerHTML = `
            <div class="loader"></div>
            <p style="color: white; font-size: 1.5rem; margin-top: 20px;">.....إنتظر قليلاً</p>
        `;
    loadingAnimation.style.position = "fixed";
    loadingAnimation.style.top = "50%";
    loadingAnimation.style.left = "50%";
    loadingAnimation.style.transform = "translate(-50%, -50%)";
    loadingAnimation.style.zIndex = "9999";
    loadingAnimation.style.display = "flex";
    loadingAnimation.style.flexDirection = "column";
    loadingAnimation.style.justifyContent = "center";
    loadingAnimation.style.alignItems = "center";
    loadingAnimation.style.background = "rgba(0, 0, 0, 0.8)";
    loadingAnimation.style.width = "100vw";
    loadingAnimation.style.height = "100vh";

    const loader = loadingAnimation.querySelector(".loader");
    loader.style.border = "16px solid #f3f3f3";
    loader.style.borderTop = "16px solid rgb(197, 79, 0)";
    loader.style.borderRadius = "50%";
    loader.style.width = "120px";
    loader.style.height = "120px";
    loader.style.animation = "spin 2s linear infinite";

    const style = document.createElement("style");
    style.innerHTML = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
    document.head.appendChild(style);

    document.body.appendChild(loadingAnimation);

    return loadingAnimation;
  }

  // Function to redirect to the index page after a delay
  function redirectToIndex(loadingAnimation, delay = 1000) {
    setTimeout(() => {
      document.body.removeChild(loadingAnimation);
      window.location.href = "/index";
    }, delay);
  }
  /// Function to add a background image to the page
  function addBackgroundImage(path) {
    document.body.style.backgroundImage =
      // url from the passed variable (path)
      `url(${path})`;
    document.body.style.backgroundSize = "cover";
    document.body.style.backgroundPosition = "center";
    document.body.style.backgroundRepeat = "no-repeat";
    addOverlay(); // Call the function to add the overlay
  }
  // Function to add a gradient overlay to the background image
  function addOverlay() {
    /// Add a gradient overlay to the background image
    const overlay = document.createElement("div");
    overlay.style.position = "fixed";
    overlay.style.top = "0";
    overlay.style.left = "0";
    overlay.style.width = "100%";
    overlay.style.height = "100%";
    overlay.style.background =
      "linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5))";
    overlay.style.zIndex = "-1"; // Behind the content
    document.body.appendChild(overlay);
  }
  // Function to set body styles
  function setBodyStyles() {
    document.body.style.height = "100vh";
    document.body.style.overflow = "hidden"; // Prevent scrolling
    document.body.style.display = "flex";
    document.body.style.justifyContent = "center";
    document.body.style.alignItems = "center";
    document.body.style.color = "white";
    document.body.style.textAlign = "center";
    document.body.style.position = "relative";
    document.body.style.zIndex = "1";
    //font shadow to black
    document.body.style.textShadow = "2px 2px 4px rgba(0, 0, 0, 0.5)";
    document.body.style.transition = "background 0.5s ease-in-out";
    document.body.style.overflowY = "hidden"; // Prevent vertical scrolling
    document.body.style.overflowX = "hidden"; // Prevent horizontal scrolling
  }

  let audioPlayed = false;

  button.addEventListener("click", () => {
    if (!audioPlayed) {
      playAudio();
      audioPlayed = true;
    }
    button.disabled = true; // Disable the button
    const loadingAnimation = showLoadingAnimation(); // Show loading animation
    audio.addEventListener("ended", () => {
      redirectToIndex(loadingAnimation); // Redirect after audio ends
    });
  });
});
