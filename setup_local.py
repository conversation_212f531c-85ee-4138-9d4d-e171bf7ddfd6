import os
import shutil
from pathlib import Path

def setup_local_environment():
    # Get the base directory
    BASE_DIR = Path(__file__).resolve().parent

    # Create necessary directories
    directories = [
        'staticfiles',
        'media',
        'logs',
        'session_data'
    ]

    for directory in directories:
        os.makedirs(BASE_DIR / directory, exist_ok=True)
        print(f"Created directory: {directory}")

    # Check for static files
    static_dir = BASE_DIR / "KLC_App/static"
    if not static_dir.exists():
        print("Error: Static files directory not found!")
        return False

    # Check for templates
    templates_dir = BASE_DIR / "KLC_App/templates"
    if not templates_dir.exists():
        print("Error: Templates directory not found!")
        return False

    # Check for firebase credentials
    firebase_key = BASE_DIR / "KLC_App/settings/firebase_key.json"
    if not firebase_key.exists():
        print("Warning: Firebase credentials not found!")
        print("Please place your firebase_key.json in KLC_App/settings/")

    print("\nSetup completed successfully!")
    print("\nNext steps:")
    print("1. Run: python manage.py collectstatic")
    print("2. Run: python manage.py migrate")
    print("3. Run: python manage.py runserver")
    return True

if __name__ == "__main__":
    setup_local_environment() 