<!-- Transaction Service Modal -->
<div
  class="modal fade"
  id="transactionsModal"
  tabindex="-1"
  aria-labelledby="transactionsModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header bg-dark text-white">
        <button
          type="button"
          class="btn-close btn-close-white"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
        <h5 class="modal-title" id="transactionsModalLabel">طلب معاملة</h5>
      </div>
      <div class="modal-body">
        <form
          class="needs-validation text-center"
          action="{% url 'make_transaction' %}"
          method="POST"
          id="transactions-form"
        >
          {% csrf_token %}
          <!-- Form content for transaction -->
          <div class="text-danger mb-3 bg-warning bg-opacity-25 p-2 rounded">
            * تنويه: عند إختيارك لنوع الطلب، يرجى قراءة التعليمات في الأسفل قبل
            إرسال الطلب.
          </div>
          <div class="mb-3">
            <label for="transactionsFullName" class="form-label"
              >الإسم الرباعي (حسب الهوية)</label
            >
            <input
              type="text"
              class="form-control"
              id="transactionsFullName"
              name="transactionsFullName"
              required
              pattern="^[\u0621-\u064A]+( [\u0621-\u064A]+){3,5}$"
              title="الإسم الرباعي يجب أن يتكون من 4 مقاطع باللغة العربية فقط"
              oninput="this.value = this.value.replace(/[^ء-ي ]/g, '')"
              placeholder="الإسم الرباعي يجب أن يتكون من 4 مقاطع باللغة العربية فقط"
            />
          </div>
          <div class="mb-3">
            <label for="transactionsPhoneNumber" class="form-label"
              >رقم الهاتف</label
            >
            <input
              type="tel"
              class="form-control telefone-input"
              id="transactionsPhoneNumber"
              name="transactionsPhoneNumber"
              required
              placeholder="مثال:0592345678 أو مع رقم مفتاح الدولة"
              minlength="10"
              maxlength="15"
              oninput="this.value = this.value.replace(/[^0-9+]/g, '')"
              pattern="\d{10, 15}"
              title="رقم الهاتف يجب أن يتكون من أرقام فقط"
            />
          </div>
          <div class="mb-3">
            <label for="transactionType" class="form-label">نوع المعاملة</label>
            <select
              class="form-select"
              id="transactionType"
              name="transactionType"
              required
            >
              <option value="" disabled selected hidden>
                إختر نوع المعاملة
              </option>
              <option value="Route Marking">طلب تعليم طريق</option>
              <option value="Site Plan Guide">طلب دليل مخطط موقع</option>
              >
              <option value="Building Permit">طلب ترخيص مبنى</option>
              <option value="Water Line NOC">
                طلب عدم ممانعة لتقديم خدمة خط مياه
              </option>
              <option value="Electricity Line NOC">
                طلب عدم ممانعة لمد خط كهرباء
              </option>
              <option value="Proof of Residence">طلب إثبات سكن</option>
              <option value="Employment Proof">طلب إثبات عمل</option>
              <option
                value="Request for Sale Transaction (for holders of Palestinian ID) – Plot Description and Clearance Certificate"
              >
                طلب معاملات صفقة بيع (لحملة الهوية الفلسطينية) وصف قطعة، براءة
                ذمة
              </option>
              <option
                value="Request for Sale Transaction (for holders of Jerusalem ID) – Plot Description, Clearance Certificate, and Classification"
              >
                طلب معاملات صفقة بيع (لحملة هوية القدس) وصف قطعة، براءة ذمة
                وتصنيف
              </option>

              <option value="Clearance for Kushan">
                طلب معاملة براءة ذمة لاستخراج الكوشان
              </option>
              <option value="other">طلب معاملة أخرى او استفسار</option>
            </select>
          </div>
          <div class="mb-3">
            <label for="transactionAdditionalNotes" class="form-label"
              >ملاحظات إضافية</label
            >
            <textarea
              class="form-control"
              id="transactionAdditionalNotes"
              name="transactionAdditionalNotes"
              rows="3"
              placeholder="اكتب ملاحظاتك هنا... (إختياري)"
              oninput="this.value = this.value.replace(/[^ء-ي0-9 :-]/g, '')"
            ></textarea>
          </div>
          <div
            id="transactionNotes"
            class="text-danger mb-3 bg-warning bg-opacity-25 p-2 rounded"
          ></div>
          <!--Get the council contact info text-->
          {% include 'KLC_App/council_contact_info_for_services.html' %}
          <button type="submit" class="btn btn-dark w-100">إرسال الطلب</button>
        </form>
      </div>
    </div>
  </div>
</div>
<!-- JS contains a function for transaction form to display notes for each transaction-->
<script>
  document.addEventListener("DOMContentLoaded", function () {
    // Initialize the transaction form notes display
    displayTransactionFormNotes();
  });

  function displayTransactionFormNotes() {
    // Get the transaction type select element and notes paragraph
    const transactionTypeSelect = document.getElementById("transactionType");
    const transactionNotes = document.getElementById("transactionNotes");
    /// Map contains the notes for each transaction type
    /////////////// Maybe the following will be changed as KLC owners needs **** Sholud be discussed with them
    const transactionNotesMap = {
      "Route Marking":
        "الرجاء تحديد المسافة المطلوبة بالمتر (الطول والعرض) وذلك في خانة الملاحظات -\n" +
        "عند قدومك للمجلس لتقديم طلب تعليم طريق، يرجى إحضار الوثائق التالية:\n" +
        "- براءة الذمة المالية من المجلس\n" +
        "- صورة حديثة عن هوية مقدم الطلب أو صاحب الأرض أو الوكيل القانوني\n" +
        "- صورة عن كوشان قطعة الأرض\n" +
        "(الرسوم المطلوبة: 200 شيكل)",

      "Site Plan Guide":
        "عند قدومك للمجلس لتقديم طلب دليل مخطط موقع، يرجى إحضار الوثائق التالية: - صورة عن كوشان قطعة الأرض - صورة عن هوية المالك سارية المفعول (الرسوم المطلوبة: 20 دينار)",

      "Building Permit":
        "عند قدومك للمجلس لتقديم طلب ترخيص مبنى، يرجى إحضار الوثائق التالية: - دليل مخطط موقع معتمد - صورة عن هوية المالك سارية المفعول - صورة عن كوشان قطعة الأرض (الرسوم تحدد حسب تصنيف الأرض ومساحة المبنى وعدد الطوابق)",

      "Water Line NOC":
        "عند قدومك للمجلس لتقديم طلب عدم ممانعة لخط مياه، يرجى إحضار الوثائق التالية: - براءة الذمة المالية من المجلس - صورة عن هوية المشترك سارية المفعول (الرسوم المطلوبة: 20 شيكل)",

      "Electricity Line NOC":
        "عند قدومك للمجلس لتقديم طلب عدم ممانعة لخط كهرباء، يرجى إحضار الوثائق التالية: - رخصة البناء - براءة الذمة المالية من المجلس - صورة عن هوية المشترك سارية المفعول (الرسوم المطلوبة: 20 شيكل)",

      "Proof of Residence":
        "عند قدومك للمجلس لتقديم طلب إثبات سكن، يرجى إحضار الوثائق التالية: - براءة الذمة المالية من المجلس - صورة عن الهوية الشخصية سارية المفعول (الرسوم المطلوبة: 20 شيكل)",

      "Employment Proof":
        "عند قدومك للمجلس لتقديم طلب إثبات عمل، يرجى إحضار الوثائق التالية: - براءة الذمة المالية من المجلس - صورة عن الهوية الشخصية سارية المفعول (الرسوم المطلوبة: 20 شيكل)",

      "Request for Sale Transaction (for holders of Palestinian ID) – Plot Description and Clearance Certificate":
        "عند قدومك للمجلس لتقديم طلب صفقة بيع (لحملة الهوية الفلسطينية)، يرجى إحضار الوثائق التالية: - براءة الذمة المالية لقطعة الأرض ومالكها - صورة عن هوية المالك سارية المفعول - كوشان قطعة الأرض (الرسوم المطلوبة: 50 شيكل للبراءة + 100 شيكل لوصف القطعة)",

      "Request for Sale Transaction (for holders of Jerusalem ID) – Plot Description, Clearance Certificate, and Classification":
        "عند قدومك للمجلس لتقديم طلب صفقة بيع (لحملة هوية القدس)، يرجى إحضار الوثائق التالية: - براءة الذمة المالية لقطعة الأرض ومالكها - صورة عن هوية المالك سارية المفعول - كوشان قطعة الأرض (الرسوم المطلوبة: 50 شيكل للبراءة + 100 شيكل لوصف القطعة + 100 شيكل للتصنيف)",

      "Clearance for Kushan":
        "عند قدومك للمجلس لتقديم طلب براءة ذمة لاستخراج الكوشان، يرجى إحضار الوثائق التالية: - براءة الذمة المالية لقطعة الأرض ومالكها - صورة عن هوية المالك سارية المفعول (الرسوم المطلوبة: 20 شيكل)",

      other:
        "عند تقديم طلب معاملة أخرى أو إستفسار يجب عليك كتابة الطلب أو الإستفسار في قسم الملاحظات الإضافية.",
    };

    // Default note
    const defaultNote =
      "*يرجى إختيار نوع المعاملة من القائمة أعلاه لتظهر لك التعليمات الخاصة بها هنا.";

    // Set initial note
    transactionNotes.textContent = defaultNote;

    // Add event listener for changes
    transactionTypeSelect.addEventListener("change", function () {
      const selectedType = this.value;
      if (selectedType && transactionNotesMap[selectedType]) {
        transactionNotes.textContent = transactionNotesMap[selectedType];
      } else {
        transactionNotes.textContent = defaultNote;
      }
    });
  }
</script>
