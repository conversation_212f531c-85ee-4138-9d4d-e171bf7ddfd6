#!/bin/bash

# Exit on error
set -e

# Variables - MODIFY THESE
DOMAIN="************"  # Your domain or IP address
PROJECT_PATH="/path/to/your/KLC_django"  # Path to your Django project
DJANGO_PORT=8000  # Port your Django app runs on
USE_HTTPS=false  # Set to true if you want to set up HTTPS

# Install Nginx
echo "Installing Nginx..."
sudo apt update
sudo apt install -y nginx

# Create Nginx configuration
echo "Creating Nginx configuration..."
cat > klc_nginx.conf << EOF
server {
    listen 80;
    server_name $DOMAIN;

    $(if $USE_HTTPS; then echo "    return 301 https://\$host\$request_uri;"; fi)

    $(if ! $USE_HTTPS; then
    echo "    location /static/ {
        alias $PROJECT_PATH/staticfiles/;
        expires 30d;
        add_header Cache-Control \"public, max-age=2592000\";
    }

    location /media/ {
        alias $PROJECT_PATH/media/;
        expires 30d;
        add_header Cache-Control \"public, max-age=2592000\";
    }

    location / {
        proxy_pass http://127.0.0.1:$DJANGO_PORT;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # Additional security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection \"1; mode=block\";"
    fi)
}

$(if $USE_HTTPS; then
echo "server {
    listen 443 ssl;
    server_name $DOMAIN;

    # SSL certificates will be configured later
    # ssl_certificate /etc/ssl/certs/nginx-selfsigned.crt;
    # ssl_certificate_key /etc/ssl/private/nginx-selfsigned.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;

    location /static/ {
        alias $PROJECT_PATH/staticfiles/;
        expires 30d;
        add_header Cache-Control \"public, max-age=2592000\";
    }

    location /media/ {
        alias $PROJECT_PATH/media/;
        expires 30d;
        add_header Cache-Control \"public, max-age=2592000\";
    }

    location / {
        proxy_pass http://127.0.0.1:$DJANGO_PORT;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # Additional security headers
    add_header Strict-Transport-Security \"max-age=31536000; includeSubDomains\" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection \"1; mode=block\";
}"
fi)
EOF

# Install the configuration
echo "Installing Nginx configuration..."
sudo cp klc_nginx.conf /etc/nginx/sites-available/klc
sudo ln -sf /etc/nginx/sites-available/klc /etc/nginx/sites-enabled/

# Remove default configuration if it exists
if [ -f /etc/nginx/sites-enabled/default ]; then
    sudo rm /etc/nginx/sites-enabled/default
fi

# Test Nginx configuration
echo "Testing Nginx configuration..."
sudo nginx -t

# Set up SSL if requested
if $USE_HTTPS; then
    echo "Setting up self-signed SSL certificates..."
    sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout /etc/ssl/private/nginx-selfsigned.key \
        -out /etc/ssl/certs/nginx-selfsigned.crt \
        -subj "/CN=$DOMAIN"
    
    # Update Nginx configuration to use the certificates
    sudo sed -i "s|# ssl_certificate|ssl_certificate|g" /etc/nginx/sites-available/klc
    sudo sed -i "s|# ssl_certificate_key|ssl_certificate_key|g" /etc/nginx/sites-available/klc
    
    # Test configuration again
    sudo nginx -t
fi

# Configure firewall
echo "Configuring firewall..."
if command -v ufw &> /dev/null; then
    sudo ufw allow 80
    if $USE_HTTPS; then
        sudo ufw allow 443
    fi
fi

# Restart Nginx
echo "Restarting Nginx..."
sudo systemctl restart nginx

echo "Nginx setup complete!"
echo "Make sure your Django application is running on port $DJANGO_PORT"
echo "Command: python manage.py runserver 127.0.0.1:$DJANGO_PORT"
