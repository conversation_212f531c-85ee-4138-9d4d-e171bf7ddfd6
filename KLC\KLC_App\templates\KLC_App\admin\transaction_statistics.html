{% extends 'KLC_App/admin/admin_base.html' %}
{% load static %}
{% load custom_filters %}

{% block title %}إحصائيات الطلبات - لوحة التحكم{% endblock %}

{% block body_class %}admin-statistics{% endblock %}

{% block extra_css %}
<!-- Flatpickr CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<!-- Flatpickr Theme  -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/themes/material_blue.css">
<!-- DataTables Buttons CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap5.min.css">
<!-- DataTables Responsive CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap5.min.css">
<!-- SweetAlert2 CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
<style>
    /* RTL support for SweetAlert2 */
    .rtl-alert {
        direction: rtl;
        text-align: right;
    }
</style>
{% endblock %}

{% block content %}
{% csrf_token %}
<div class="section-header">
    <h2><i class="fas fa-chart-bar me-2"></i> سجل الطلبات المكتملة والإحصائيات</h2>
    <div>
        <a href="{% url 'admin_transactions' %}" class="btn btn-primary me-2">
            <i class="fas fa-tasks me-1"></i>
            الطلبات قيد التنفيذ
        </a>
        <a href="{% url 'admin_dashboard' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للوحة التحكم
        </a>
    </div>
</div>

<!-- Info Alert -->
<div class="alert alert-info mb-4">
    <div class="d-flex align-items-center">
        <div class="alert-icon me-3">
            <i class="fas fa-info-circle fa-2x"></i>
        </div>
        <div>
            <h5 class="alert-heading mb-1">سجل الطلبات المكتملة</h5>
            <p class="mb-0">
                يعرض هذا السجل جميع الطلبات المكتملة التي تم إدخال رقم إيصال لها. هذه الطلبات محفوظة بشكل دائم ولا يمكن حذفها.
                لحذف طلب مكتمل، يجب عليك إدخال كلمة المرور الإدارية وتأكيد الحذف.
            </p>
        </div>
    </div>
</div>

<!-- Date Filter Form -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title">
            <i class="fas fa-filter me-2"></i> تصفية حسب تاريخ الاكتمال
        </h5>
    </div>
    <div class="card-body">
        <form method="get" class="row g-3 align-items-end">
            <div class="col-md-4">
                <label for="start_date" class="form-label fw-bold">من تاريخ</label>
                <div class="date-input-container">
                    <input type="date" class="form-control flatpickr-date" id="start_date" name="start_date" value="{{ start_date }}">
                    <i class="fas fa-calendar-alt"></i>
                </div>
            </div>
            <div class="col-md-4">
                <label for="end_date" class="form-label fw-bold">إلى تاريخ</label>
                <div class="date-input-container">
                    <input type="date" class="form-control flatpickr-date" id="end_date" name="end_date" value="{{ end_date }}">
                    <i class="fas fa-calendar-alt"></i>
                </div>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-1"></i>
                    تصفية
                </button>
            </div>
            <div class="col-md-2">
                <a href="?" class="btn btn-outline-secondary w-100">إعادة تعيين</a>
            </div>
        </form>
    </div>
</div>

<!-- Transactions Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title">
            <i class="fas fa-table me-2"></i> سجل الطلبات المكتملة
            <span class="badge bg-success ms-2">مكتملة</span>
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table align-middle" id="dataTable">
                <thead>
                    <tr>
                        <th scope="col">#</th>
                        <th scope="col">رقم الهوية</th>
                        <th scope="col">الاسم</th>
                        <th scope="col">نوع الطلب</th>
                        <th scope="col">تاريخ الإنشاء</th>
                        <th scope="col">تاريخ الإكمال</th>
                        <th scope="col">رقم الإيصال</th>
                        <th scope="col">ملاحظات إضافية</th>
                        <th scope="col">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for transaction in transactions %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ transaction.id_number }}</td>
                        <td>{{ transaction.full_name }}</td>
                        <td>{{ transaction.transaction_type }}</td>
                        <td>{{ transaction.created_at }}</td>
                        <td>{{ transaction.completed_at|default:"-" }}</td>
                        <td class="receipt-number">
                            {% if transaction.receipt_number %}
                                {{ transaction.receipt_number }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>{{ transaction.additional_notes }}</td>
                        <td>
                            <button type="button" class="btn btn-danger btn-sm delete-transaction"
                                    data-transaction-id="{{ transaction.id }}"
                                    data-transaction-name="{{ transaction.full_name }}"
                                    data-transaction-receipt="{{ transaction.receipt_number }}">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="9" class="text-center py-4">
                            {% if start_date and end_date %}
                                لا توجد طلبات مكتملة في الفترة المحددة
                            {% else %}
                                لا توجد طلبات مكتملة
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Statistics Summary -->
<div class="section-header mt-4">
    <h2><i class="fas fa-chart-pie me-2"></i> ملخص الإحصائيات</h2>
</div>

<!-- Total Statistics Card -->
<div class="stats-summary mb-4">
    <div class="card total-card">
        <div class="card-body d-flex align-items-center">
            <div class="stats-icon">
                <i class="fas fa-layer-group"></i>
            </div>
            <div class="stats-info">
                <h3>{{ total_transactions }}</h3>
                <p>إجمالي الطلبات المكتملة</p>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row">
    {% for type in all_types %}
    {% with total=transactions_by_type|get_item:type|default:0 done=completed_transactions_by_type|get_item:type|default:0 perc=percent_by_type|get_item:type|default:0 %}
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card h-100 stats-card">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="stats-icon-sm me-3">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h6 class="card-title mb-0">{{ type }}</h6>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h3 class="mb-0">{{ total }}</h3>
                    <span class="badge {% if perc >= 80 %}bg-success{% elif perc >= 40 %}bg-warning{% else %}bg-danger{% endif %}">
                        {{ perc }}%
                    </span>
                </div>
                <div class="progress mb-2" style="height: 6px;">
                    <div class="progress-bar
                        {% if perc >= 80 %}bg-success
                        {% elif perc >= 40 %}bg-warning
                        {% else %}bg-danger
                        {% endif %}"
                        role="progressbar"
                        style="width:{{ perc }}%"
                        aria-valuenow="{{ done }}"
                        aria-valuemin="0" aria-valuemax="{{ total }}">
                    </div>
                </div>
                <div class="small text-muted">
                    <i class="fas fa-check-circle text-success me-1"></i>
                    {{ done }} منجز من أصل {{ total }}
                </div>
            </div>
        </div>
    </div>
    {% endwith %}
    {% endfor %}
</div>

<style>

    /* Section Header Styling */
    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid #e2e8f0;
    }

    [data-theme="dark"] .section-header {
        border-bottom-color: #334155;
    }

    .section-header h2 {
        font-size: 1.5rem;
        font-weight: 700;
        color: #1e293b;
        margin: 0;
    }

    [data-theme="dark"] .section-header h2 {
        color: #f8fafc;
    }

    /* Card Styling */
    .card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        overflow: hidden;
        margin-bottom: 1.5rem;
        background-color: #fff;
    }

    [data-theme="dark"] .card {
        background-color: rgba(30, 41, 59, 0.8);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.12);
    }

    .card-header {
        padding: 1rem 1.5rem;
        background-color: #f8fafc;
        border-bottom: 1px solid #e2e8f0;
    }

    [data-theme="dark"] .card-header {
        background-color: rgba(15, 23, 42, 0.8);
        border-bottom-color: #334155;
    }

    .card-title {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: #1e293b;
    }

    [data-theme="dark"] .card-title {
        color: #f8fafc;
    }

    /* Date Input Styling */
    .date-input-container {
        position: relative;
    }

    .date-input-container i {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #64748b;
        cursor: pointer;
    }

    [data-theme="dark"] .date-input-container i {
        color: #94a3b8;
    }

    .date-input-container input {
        padding-left: 35px;
        cursor: pointer;
        background-color: #fff;
        color: #1e293b;
        border: 1px solid #e2e8f0;
    }

    [data-theme="dark"] .date-input-container input {
        background-color: #1e293b;
        color: #f8fafc;
        border-color: #475569;
    }

    /* Form Label Styling */
    .form-label {
        color: #1e293b;
        font-weight: 600;
    }

    [data-theme="dark"] .form-label {
        color: #f8fafc;
    }

    /* Statistics Cards Styling */
    .stats-summary {
        margin-bottom: 2rem;
    }

    .total-card {
        background: linear-gradient(90deg, #2563eb 0%, #3b82f6 100%) !important;
        color: black !important;
        border: none;
        border-radius: 10px;
    }

    .total-card .stats-info h3,
    .total-card .stats-info p,
    .total-card .stats-icon i {
        color: black !important;
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        margin-right: 1.5rem;
    }

    .stats-info h3 {
        font-size: 2.2rem;
        font-weight: 700;
        margin-bottom: 0.2rem;
    }

    .stats-info p {
        font-size: 1rem;
        margin-bottom: 0;
        opacity: 0.8;
    }

    .stats-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        background-color: #fff;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    [data-theme="dark"] .stats-card {
        background-color: rgba(30, 41, 59, 0.8);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    [data-theme="dark"] .stats-card:hover {
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
    }

    [data-theme="dark"] .stats-card .card-title,
    [data-theme="dark"] .stats-card h3 {
        color: #e2e8f0;
    }

    [data-theme="dark"] .stats-card .text-muted {
        color: #94a3b8 !important;
    }

    .stats-icon-sm {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        background: #e0e7ff;
        color: #2563eb;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
    }

    /* Table Styling */
    .table {
        --bs-table-bg: transparent;
    }

    [data-theme="dark"] .table {
        --bs-table-bg: rgba(30, 41, 59, 0.8);
        color: #e2e8f0;
    }

    [data-theme="dark"] .table thead th {
        background: #1e40af !important;
        color: #fff !important;
    }

    [data-theme="dark"] .table tbody tr:nth-of-type(odd) {
        background-color: rgba(15, 23, 42, 0.6);
    }

    [data-theme="dark"] .table tbody tr:nth-of-type(even) {
        background-color: rgba(30, 41, 59, 0.6);
    }

    [data-theme="dark"] .table tbody tr:hover {
        background-color: rgba(51, 65, 85, 0.7) !important;
    }

    .receipt-number {
        font-weight: 600;
        color:rgb(0, 0, 0);
    }

    [data-theme="dark"] .receipt-number {
        color:rgb(255, 255, 255);
    }

    /* Delete button styling */
    .delete-transaction {
        width: 36px;
        height: 36px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .delete-transaction:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
    }

    /* Password modal styling */
    #passwordModal .modal-content {
        border: none;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        max-width: 500px;
        margin: 0 auto;
        position: relative;
        animation: modalFadeIn 0.3s ease-out;
    }

    @keyframes modalFadeIn {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    #passwordModal .modal-dialog {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: calc(100% - 3.5rem);
    }

    #passwordModal .modal-header {
        border-bottom: 3px solid #dc3545;
        padding: 1.25rem 1.5rem;
    }

    #passwordModal .modal-body {
        padding: 1.5rem;
    }

    #passwordModal .modal-footer {
        padding: 1.25rem 1.5rem;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
    }

    #passwordModal .alert-warning {
        border-right: 4px solid #ffc107;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }

    /* Transaction details styling */
    .transaction-details {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 1.25rem;
        border: 1px solid #e9ecef;
    }

    [data-theme="dark"] .transaction-details {
        background-color: #334155;
        border-color: #475569;
    }

    .transaction-detail-item {
        display: flex;
        margin-bottom: 0.75rem;
    }

    .transaction-detail-item:last-child {
        margin-bottom: 0;
    }

    .detail-label {
        font-weight: 600;
        min-width: 100px;
        color: #495057;
    }

    [data-theme="dark"] .detail-label {
        color: #e2e8f0;
    }

    .detail-value {
        font-weight: 500;
        color: #212529;
    }

    [data-theme="dark"] .detail-value {
        color: #f8fafc;
    }

    .password-hint {
        color: #6c757d;
        padding-right: 0.5rem;
    }

    [data-theme="dark"] .password-hint {
        color: #94a3b8;
    }

    #passwordModal .form-label {
        font-weight: 600;
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }

    #passwordModal .input-group {
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
        border-radius: 8px;
        overflow: hidden;
    }

    #passwordModal .form-control {
        border: 1px solid #e2e8f0;
        padding: 0.75rem 1rem;
        font-size: 1rem;
    }

    #passwordModal .form-control:focus {
        box-shadow: none;
        border-color: #3b82f6;
    }

    #passwordModal .toggle-password {
        cursor: pointer;
        background-color: #f8fafc;
        border-color: #e2e8f0;
        border-right: none;
        padding: 0.75rem 1rem;
    }

    #passwordModal .toggle-password:hover {
        background-color: #f1f5f9;
    }

    #passwordModal .invalid-feedback {
        display: none;
        font-weight: 500;
        margin-top: 0.5rem;
        padding-right: 0.5rem;
    }

    #passwordModal .btn-danger {
        background-color: #dc3545;
        border-color: #dc3545;
        padding: 0.6rem 1.25rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    #passwordModal .btn-danger:hover {
        background-color: #c82333;
        border-color: #bd2130;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
    }

    #passwordModal .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
        padding: 0.6rem 1.25rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    #passwordModal .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #545b62;
    }

    /* Dark mode styles */
    [data-theme="dark"] #passwordModal .modal-content {
        background-color: #1e293b;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
    }

    [data-theme="dark"] #passwordModal .modal-body,
    [data-theme="dark"] #passwordModal .form-label {
        color: #e2e8f0;
    }

    [data-theme="dark"] #passwordModal .modal-footer {
        border-top-color: rgba(255, 255, 255, 0.1);
    }

    [data-theme="dark"] #passwordModal .alert-warning {
        background-color: rgba(255, 193, 7, 0.1);
        color: #ffc107;
        border-color: #ffc107;
    }

    [data-theme="dark"] #passwordModal .form-control {
        background-color: #334155;
        border-color: #475569;
        color: #e2e8f0;
    }

    [data-theme="dark"] #passwordModal .form-control:focus {
        border-color: #3b82f6;
    }

    [data-theme="dark"] #passwordModal .toggle-password {
        background-color: #334155;
        border-color: #475569;
        color: #e2e8f0;
    }

    [data-theme="dark"] #passwordModal .toggle-password:hover {
        background-color: #1e293b;
    }

    /* Flatpickr Calendar Styling */
    .flatpickr-calendar {
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        border-radius: 8px;
        font-family: 'Cairo', 'Tajawal', sans-serif;
        width: 300px !important;
        direction: rtl;
        background: #fff;
        z-index: 9999 !important;
    }

    .flatpickr-months {
        padding-top: 8px;
        padding-bottom: 8px;
        background-color: #2563eb;
        color: white;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
    }

    .flatpickr-month {
        height: 40px;
    }

    .flatpickr-current-month {
        font-size: 16px;
        font-weight: 600;
        padding-top: 0;
    }

    .flatpickr-weekday {
        background-color: #f8fafc;
        color: #64748b;
        font-weight: 600;
    }

    .flatpickr-day {
        border-radius: 6px;
        margin: 2px;
        color: #334155;
    }

    .flatpickr-day.selected {
        background: #2563eb;
        border-color: #2563eb;
        color: white;
    }

    .flatpickr-day.today {
        border-color: #2563eb;
        color: #2563eb;
        font-weight: 700;
    }

    .flatpickr-day:hover {
        background: #e0e7ff;
    }

    .flatpickr-day.flatpickr-disabled {
        color: #cbd5e1 !important;
    }

    /* DataTables Controls Styling */
    .dt-controls {
        background: rgba(255, 255, 255, 0.8);
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }

    [data-theme="dark"] .dt-controls {
        background: rgba(30, 41, 59, 0.8);
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }

    .dataTables_filter label {
        display: flex;
        align-items: center;
        position: relative;
        margin-bottom: 0;
        color: #1e293b;
    }

    [data-theme="dark"] .dataTables_filter label {
        color: #f8fafc;
    }

    .dataTables_filter input {
        border-radius: 8px;
        padding: 0.5rem 2.5rem 0.5rem 1rem;
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;
        width: 250px;
        background-color: #fff;
        color: #1e293b;
    }

    [data-theme="dark"] .dataTables_filter input {
        background-color: #1e293b;
        border-color: #475569;
        color: #f8fafc;
    }

    .dataTables_filter input:focus {
        border-color: #2563eb;
        box-shadow: 0 0 0 3px rgba(37,99,235,0.2);
        outline: none;
    }

    .search-icon {
        position: absolute;
        right: 10px;
        color: #64748b;
    }

    [data-theme="dark"] .search-icon {
        color: #94a3b8;
    }

    .dataTables_length select {
        border-radius: 8px;
        padding: 0.5rem;
        border: 1px solid #e2e8f0;
        background-color: white;
        color: #1e293b;
    }

    [data-theme="dark"] .dataTables_length select {
        background-color: #1e293b;
        border-color: #475569;
        color: #f8fafc;
    }

    .dataTables_length label {
        color: #1e293b;
    }

    [data-theme="dark"] .dataTables_length label {
        color: #f8fafc;
    }

    .dataTables_paginate .paginate_button {
        border-radius: 8px !important;
        margin: 0 2px;
    }

    .dataTables_paginate .paginate_button.current {
        background: #2563eb !important;
        border-color: #2563eb !important;
        color: white !important;
    }

    [data-theme="dark"] .dataTables_paginate .paginate_button.current {
        background: #3b82f6 !important;
        border-color: #3b82f6 !important;
    }

    .dataTables_paginate .paginate_button:hover {
        background: #e0e7ff !important;
        border-color: #e0e7ff !important;
        color: #2563eb !important;
    }

    [data-theme="dark"] .dataTables_paginate .paginate_button:hover {
        background: #1e3a8a !important;
        border-color: #1e3a8a !important;
        color: #60a5fa !important;
    }

    .dataTables_info {
        color: #64748b;
        padding: 0.5rem 1rem;
        background: rgba(255, 255, 255, 0.5);
        border-radius: 8px;
    }

    [data-theme="dark"] .dataTables_info {
        color: #94a3b8;
        background: rgba(30, 41, 59, 0.5);
    }

    /* DataTables Buttons Styling */
    .dt-buttons .btn {
        margin-right: 0.5rem;
    }

    [data-theme="dark"] .dt-buttons .btn-secondary {
        background-color:rgb(255, 8, 8);
        border-color:rgb(255, 8, 8);
    }

    [data-theme="dark"] .dt-buttons .btn-success {
        background-color:rgb(0, 255, 170);
        border-color:rgb(0, 255, 170);
    }

    [data-theme="dark"] .dt-buttons .btn-primary {
        background-color:rgb(255, 8, 8);
        border-color:rgb(255, 8, 8);
    }
</style>

<!-- Password Confirmation Modal -->
<div class="modal fade" id="passwordModal" tabindex="-1" aria-labelledby="passwordModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="passwordModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i> تأكيد حذف الطلب المكتمل
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <strong>تحذير:</strong> أنت على وشك حذف طلب مكتمل له رقم إيصال. هذا الإجراء لا يمكن التراجع عنه.
                </div>

                <div class="transaction-details mb-4">
                    <div class="transaction-detail-item">
                        <span class="detail-label">الطلب:</span>
                        <span class="detail-value" id="transactionName"></span>
                    </div>
                    <div class="transaction-detail-item">
                        <span class="detail-label">رقم الإيصال:</span>
                        <span class="detail-value" id="receiptNumber"></span>
                    </div>
                </div>

                <div class="password-section">
                    <label for="adminPassword" class="form-label">كلمة المرور الإدارية:</label>
                    <div class="input-group mb-2">
                        <input type="password" class="form-control" id="adminPassword" placeholder="أدخل كلمة المرور الإدارية">
                        <button class="btn btn-outline-secondary toggle-password" type="button">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="password-hint text-muted mb-3">
                        <small><i class="fas fa-info-circle me-1"></i> كلمة المرور مطلوبة للتأكيد على صلاحية الحذف</small>
                    </div>
                    <div class="invalid-feedback" id="passwordError">
                        كلمة المرور غير صحيحة
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> إلغاء
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash-alt me-1"></i> تأكيد الحذف
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables Buttons JS -->
<script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.bootstrap5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.2.9/js/responsive.bootstrap5.min.js"></script>

<!-- flatpickr JS -->
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ar.js"></script>

<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    $(document).ready(function() {
        // Initialize DataTable with professional styling
        $('#dataTable').DataTable({
            language: {
                url: "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
            },
            responsive: true,
            dom: '<"dt-controls"<"row"<"col-md-6"l><"col-md-6"f>>>' +
                 '<"row"<"col-sm-12"tr>>' +
                 '<"row mt-3"<"col-sm-5"i><"col-sm-7"p>>',
            pageLength: 10,
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]],
            order: [[4, "desc"]], // Sort by creation date by default
            columnDefs: [
                { type: "date", targets: [4, 5] } // Specify date columns for proper sorting
            ],
            buttons: [
                {
                    extend: 'copyHtml5',
                    text: '<i class="fas fa-copy"></i> نسخ',
                    className: 'btn btn-secondary btn-sm'
                },
                {
                    extend: 'excelHtml5',
                    text: '<i class="fas fa-file-excel"></i> تصدير Excel',
                    className: 'btn btn-success btn-sm'
                },
                {
                    extend: 'print',
                    text: '<i class="fas fa-print"></i> طباعة',
                    className: 'btn btn-primary btn-sm'
                }
            ],
            initComplete: function() {
                // Add buttons to the DataTable
                new $.fn.dataTable.Buttons(this, {
                    buttons: [
                        {
                            extend: 'copyHtml5',
                            text: '<i class="fas fa-copy"></i> نسخ',
                            className: 'btn btn-secondary btn-sm'
                        },
                        {
                            extend: 'excelHtml5',
                            text: '<i class="fas fa-file-excel"></i> تصدير Excel',
                            className: 'btn btn-success btn-sm'
                        },
                        {
                            extend: 'print',
                            text: '<i class="fas fa-print"></i> طباعة',
                            className: 'btn btn-primary btn-sm'
                        }
                    ]
                });

                this.api().buttons().container().appendTo($('.dt-controls'));

                // Add placeholder to search input
                $('.dataTables_filter input').attr('placeholder', 'البحث...');
            }
        });

        // Common flatpickr config options
        const commonConfig = {
            dateFormat: "Y-m-d",
            locale: "ar",
            disableMobile: true,
            maxDate: "today", // Only allow past dates (today and before)
            static: false,
            monthSelectorType: "dropdown",
            showMonths: 1,
            position: "auto",
            ariaDateFormat: "Y-m-d",
            animate: true,
            showDaysInNextAndPreviousMonths: true,
            fixedHeight: true,
            prevArrow: '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"><path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"></path></svg>',
            nextArrow: '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"><path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"></path></svg>'
        };

        // Initialize start date picker
        const startDatePicker = flatpickr("#start_date", {
            ...commonConfig,
            onChange: function(selectedDates, dateStr) {
                if (selectedDates.length > 0) {
                    // Update end date min date to match start date
                    endDatePicker.set('minDate', selectedDates[0]);

                    // Clear end date if it's before start date
                    if (endDatePicker.selectedDates.length > 0 &&
                        endDatePicker.selectedDates[0] < selectedDates[0]) {
                        endDatePicker.clear();
                    }
                }
            }
        });

        // Initialize end date picker
        const endDatePicker = flatpickr("#end_date", {
            ...commonConfig,
            onChange: function(selectedDates, dateStr) {
                if (selectedDates.length > 0) {
                    // Update start date max date to match end date
                    startDatePicker.set('maxDate', selectedDates[0]);

                    // Clear start date if it's after end date
                    if (startDatePicker.selectedDates.length > 0 &&
                        startDatePicker.selectedDates[0] > selectedDates[0]) {
                        startDatePicker.clear();
                    }
                } else {
                    // Reset start date max date to today if end date is cleared
                    startDatePicker.set('maxDate', 'today');
                }
            }
        });

        // Add click event to calendar icons
        $('.date-input-container i').on('click', function() {
            const input = $(this).siblings('input')[0];
            if (input.id === 'start_date') {
                startDatePicker.open();
            } else if (input.id === 'end_date') {
                endDatePicker.open();
            }
        });

        // Handle delete transaction button click
        let transactionIdToDelete = null;

        $('.delete-transaction').on('click', function() {
            // Get transaction details
            transactionIdToDelete = $(this).data('transaction-id');
            const transactionName = $(this).data('transaction-name');
            const receiptNumber = $(this).data('transaction-receipt');

            // Check if current admin is authorized (only 'ahmad' has permission)
            const currentAdmin = '{{ request.session.admin_username }}';
            if (currentAdmin !== 'ahmad') {
                // Show unauthorized message
                Swal.fire({
                    title: 'غير مصرح',
                    text: 'ليس لديك صلاحية لحذف المعاملات المؤكدة. هذه العملية متاحة فقط للمشرف "ahmad".',
                    icon: 'error',
                    confirmButtonText: 'حسناً',
                    confirmButtonColor: '#dc3545',
                    customClass: {
                        popup: 'rtl-alert'
                    }
                });
                return;
            }

            // Set modal content
            $('#transactionName').text(transactionName);
            $('#receiptNumber').text(receiptNumber || 'غير متوفر');

            // Reset password field and error
            $('#adminPassword').val('').removeClass('is-invalid');
            $('#passwordError').hide();

            // Show modal
            $('#passwordModal').modal('show');
        });

        // Focus on password field after modal is shown
        $('#passwordModal').on('shown.bs.modal', function() {
            $('#adminPassword').focus();
        });

        // Toggle password visibility
        $('.toggle-password').on('click', function() {
            const passwordInput = $('#adminPassword');
            const icon = $(this).find('i');

            if (passwordInput.attr('type') === 'password') {
                passwordInput.attr('type', 'text');
                icon.removeClass('fa-eye').addClass('fa-eye-slash');
            } else {
                passwordInput.attr('type', 'password');
                icon.removeClass('fa-eye-slash').addClass('fa-eye');
            }
        });

        // Handle confirm delete button click
        $('#confirmDeleteBtn').on('click', function() {
            const password = $('#adminPassword').val();

            if (password) {
                // Send password to server for verification
                if (transactionIdToDelete) {
                    // Send AJAX request to delete the transaction
                    $.ajax({
                        url: "{% url 'delete_completed_transaction' 0 %}".replace('0', transactionIdToDelete),
                        type: 'POST',
                        data: {
                            'csrfmiddlewaretoken': $('input[name=csrfmiddlewaretoken]').val(),
                            'manager_password': password
                        },
                        success: function(response) {
                            if (response.status === 'success') {
                                // Close the modal and reload the page
                                $('#passwordModal').modal('hide');

                                // Show success message
                                const alertHtml = `
                                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                                        <i class="fas fa-check-circle me-2"></i>
                                        تم حذف الطلب بنجاح
                                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                    </div>
                                `;

                                // Add alert before the filter card
                                $('.card.mb-4').first().before(alertHtml);

                                // Remove the row from the table
                                $(`button[data-transaction-id="${transactionIdToDelete}"]`).closest('tr').fadeOut(500, function() {
                                    $(this).remove();

                                    // Reload the page after a short delay to update statistics
                                    setTimeout(function() {
                                        location.reload();
                                    }, 2000);
                                });
                            } else {
                                // Show error message
                                if (response.message === 'كلمة المرور غير صحيحة') {
                                    // Password is incorrect, show error in the modal
                                    $('#adminPassword').addClass('is-invalid');
                                    $('#passwordError').text('كلمة المرور غير صحيحة').show();
                                } else {
                                    // Other error, show alert
                                    alert(response.message || 'حدث خطأ أثناء حذف الطلب');
                                }
                            }
                        },
                        error: function() {
                            alert('حدث خطأ أثناء الاتصال بالخادم');
                        }
                    });
                }
            } else {
                // No password entered, show error
                $('#adminPassword').addClass('is-invalid');
                $('#passwordError').text('يرجى إدخال كلمة المرور').show();
            }
        });

        // Handle Enter key in password field
        $('#adminPassword').on('keypress', function(e) {
            if (e.which === 13) {
                e.preventDefault();
                $('#confirmDeleteBtn').click();
            }
        });
    });
</script>
{% endblock %}
