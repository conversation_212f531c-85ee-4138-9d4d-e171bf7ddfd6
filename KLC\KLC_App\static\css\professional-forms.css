/* Professional Forms CSS
   A comprehensive styling solution for all forms in the KLC project
   This file provides consistent, modern, and professional styling for all form elements
*/

/* Base Form Styling */
.professional-form {
    --form-primary: #3498db;
    --form-primary-hover: #2980b9;
    --form-success: #2ecc71;
    --form-danger: #e74c3c;
    --form-warning: #f39c12;
    --form-info: #3498db;
    --form-border: #e0e0e0;
    --form-shadow: rgba(0, 0, 0, 0.1);
    --form-text: #333;
    --form-text-light: #777;
    --form-bg: #fff;
    --form-bg-light: #f8f9fa;

    background-color: var(--form-bg);
    border-radius: 12px;
    box-shadow: 0 8px 30px var(--form-shadow);
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: visible;
    border: 1px solid var(--form-border);
}

/* Dark theme support */
[data-theme="dark"] .professional-form {
    --form-primary: #3498db;
    --form-primary-hover: #2980b9;
    --form-success: #2ecc71;
    --form-danger: #e74c3c;
    --form-warning: #f39c12;
    --form-info: #3498db;
    --form-border: #2c3e50;
    --form-shadow: rgba(0, 0, 0, 0.3);
    --form-text: #ecf0f1;
    --form-text-light: #bdc3c7;
    --form-bg: #34495e;
    --form-bg-light: #2c3e50;
}

/* Form Header */
.professional-form .form-header {
    margin-bottom: 1.5rem;
    text-align: center;
    position: relative;
}

.professional-form .form-header h3 {
    font-weight: 600;
    color: var(--form-text);
    margin-bottom: 0.5rem;
}

.professional-form .form-header p {
    color: var(--form-text-light);
    font-size: 0.95rem;
}

/* Form Groups */
.professional-form .form-group,
.professional-form .mb-3 {
    margin-bottom: 1.5rem;
    position: relative;
}

/* Form Labels */
.professional-form label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    display: block;
    color: var(--form-text);
    transition: all 0.3s ease;
}

.professional-form .form-control:focus ~ label,
.professional-form .form-select:focus ~ label {
    color: var(--form-primary);
}

/* Form Controls */
.professional-form .form-control,
.professional-form .form-select {
    border-radius: 8px;
    padding: 0.75rem 1rem;
    border: 2px solid var(--form-border);
    background-color: var(--form-bg);
    color: var(--form-text);
    transition: all 0.3s ease;
    font-size: 1rem;
    width: 100%;
    z-index: 1;
    position: relative;
}

.professional-form .form-control:focus,
.professional-form .form-select:focus {
    border-color: var(--form-primary);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
    outline: none;
}

.professional-form .form-control::placeholder {
    color: var(--form-text-light);
    opacity: 0.7;
}

/* Input Groups */
.professional-form .input-group {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    z-index: 1;
    position: relative;
}

.professional-form .input-group-text {
    background-color: var(--form-bg-light);
    border: 2px solid var(--form-border);
    color: var(--form-text);
    padding: 0.75rem 1rem;
}

.professional-form .input-group .form-control {
    border-radius: 0 8px 8px 0;
}

.professional-form .input-group .input-group-text + .form-control {
    border-left: 0;
}

/* Form Buttons */
.professional-form .btn {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    font-size: 1rem;
}

.professional-form .btn-primary {
    background-color: var(--form-primary);
    border-color: var(--form-primary);
    color: white;
}

.professional-form .btn-primary:hover {
    background-color: var(--form-primary-hover);
    border-color: var(--form-primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.professional-form .btn-danger {
    background-color: var(--form-danger);
    border-color: var(--form-danger);
    color: white;
}

.professional-form .btn-danger:hover {
    background-color: #c0392b;
    border-color: #c0392b;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

.professional-form .btn-success {
    background-color: var(--form-success);
    border-color: var(--form-success);
    color: white;
}

.professional-form .btn-success:hover {
    background-color: #27ae60;
    border-color: #27ae60;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(46, 204, 113, 0.3);
}

/* Form Validation */
.professional-form .form-control.is-invalid {
    border-color: var(--form-danger);
    background-image: none;
    padding-right: 1rem;
}

.professional-form .form-control.is-valid {
    border-color: var(--form-success);
    background-image: none;
    padding-right: 1rem;
}

.professional-form .invalid-feedback {
    color: var(--form-danger);
    font-size: 0.85rem;
    margin-top: 0.5rem;
}

.professional-form .valid-feedback {
    color: var(--form-success);
    font-size: 0.85rem;
    margin-top: 0.5rem;
}

/* Form Checkboxes and Radios */
.professional-form .form-check {
    padding-left: 1.8rem;
    margin-bottom: 0.75rem;
}

.professional-form .form-check-input {
    width: 1.2rem;
    height: 1.2rem;
    margin-left: -1.8rem;
    margin-top: 0.15rem;
    border: 2px solid var(--form-border);
    background-color: var(--form-bg);
}

.professional-form .form-check-input:checked {
    background-color: var(--form-primary);
    border-color: var(--form-primary);
}

.professional-form .form-check-input:focus {
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
    border-color: var(--form-primary);
}

.professional-form .form-check-label {
    color: var(--form-text);
    font-weight: normal;
}

/* Form Help Text */
.professional-form .form-text {
    color: var(--form-text-light);
    font-size: 0.85rem;
    margin-top: 0.5rem;
}

/* Form Alerts */
.professional-form .alert {
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    border: none;
}

.professional-form .alert-primary {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--form-primary);
}

.professional-form .alert-danger {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--form-danger);
}

.professional-form .alert-success {
    background-color: rgba(46, 204, 113, 0.1);
    color: var(--form-success);
}

.professional-form .alert-warning {
    background-color: rgba(243, 156, 18, 0.1);
    color: var(--form-warning);
}

/* Form Dividers */
.professional-form .form-divider {
    display: flex;
    align-items: center;
    margin: 1.5rem 0;
    color: var(--form-text-light);
}

.professional-form .form-divider::before,
.professional-form .form-divider::after {
    content: "";
    flex: 1;
    height: 1px;
    background-color: var(--form-border);
}

.professional-form .form-divider::before {
    margin-right: 1rem;
}

.professional-form .form-divider::after {
    margin-left: 1rem;
}

/* Form Textarea */
.professional-form textarea.form-control {
    min-height: 120px;
    resize: vertical;
}

/* Form Select */
.professional-form select.form-select {
    background-position: right 1rem center;
    padding-right: 2.5rem;
}

/* Form File Input */
.professional-form .form-control[type="file"] {
    padding: 0.5rem;
}

.professional-form .form-control[type="file"]::file-selector-button {
    padding: 0.375rem 0.75rem;
    margin: -0.375rem -0.75rem;
    margin-right: 0.75rem;
    color: var(--form-text);
    background-color: var(--form-bg-light);
    border: 0;
    border-right: 1px solid var(--form-border);
    border-radius: 0;
    transition: all 0.3s ease;
}

.professional-form .form-control[type="file"]:hover::file-selector-button {
    background-color: var(--form-primary);
    color: white;
}

/* Form Date/Time Input */
.professional-form input[type="date"],
.professional-form input[type="time"],
.professional-form input[type="datetime-local"] {
    min-height: calc(1.5em + 0.75rem + 2px);
}

/* Form Range Input */
.professional-form input[type="range"] {
    height: 1.5rem;
    padding: 0;
}

/* Form Color Input */
.professional-form input[type="color"] {
    height: 2.5rem;
    padding: 0.25rem;
    width: 4rem;
}

/* Form Floating Labels */
.professional-form .form-floating {
    position: relative;
}

.professional-form .form-floating > .form-control,
.professional-form .form-floating > .form-select {
    height: calc(3.5rem + 2px);
    padding: 1.5rem 1rem 0.5rem;
}

.professional-form .form-floating > label {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 1rem;
    pointer-events: none;
    border: 1px solid transparent;
    transform-origin: 0 0;
    transition: all 0.3s ease;
    color: var(--form-text-light);
}

.professional-form .form-floating > .form-control:focus ~ label,
.professional-form .form-floating > .form-control:not(:placeholder-shown) ~ label,
.professional-form .form-floating > .form-select ~ label {
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    color: var(--form-primary);
}

/* Form in Modal */
.modal .professional-form {
    box-shadow: none;
    padding: 0;
    border: none;
}

/* Form in Card */
.card .professional-form {
    box-shadow: none;
    padding: 1rem;
    border: none;
}

/* Form Responsive Adjustments */
@media (max-width: 768px) {
    .professional-form {
        padding: 1.25rem;
        border-radius: 8px;
        margin-bottom: 1rem;
    }

    .professional-form .form-header {
        margin-bottom: 1rem;
    }

    .professional-form .form-header h3 {
        font-size: 1.25rem;
    }

    .professional-form .form-group,
    .professional-form .mb-3 {
        margin-bottom: 1rem;
    }

    .professional-form .form-control,
    .professional-form .form-select {
        padding: 0.6rem 0.8rem;
        font-size: 0.95rem;
    }

    .professional-form .input-group-text {
        padding: 0.6rem 0.8rem;
    }

    .professional-form .btn {
        width: 100%;
        margin-bottom: 0.5rem;
        padding: 0.6rem 1rem;
        font-size: 0.95rem;
    }

    .professional-form .form-check-input {
        width: 1.1rem;
        height: 1.1rem;
    }

    .professional-form .form-divider {
        margin: 1rem 0;
    }

    .professional-form textarea.form-control {
        min-height: 100px;
    }

    .professional-form .alert {
        padding: 0.75rem;
        margin-bottom: 1rem;
    }

    .professional-form .form-floating > .form-control,
    .professional-form .form-floating > .form-select {
        height: calc(3.2rem + 2px);
        padding: 1.3rem 0.8rem 0.4rem;
    }

    /* Improve touch targets for mobile */
    .professional-form .form-check-input {
        margin-top: 0.2rem;
    }

    /* Better spacing for action buttons on mobile */
    .professional-form .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    /* Adjust modal forms on mobile */
    .modal .professional-form {
        padding: 0;
    }

    /* Make sure form elements don't overflow on small screens */
    .professional-form .row {
        margin-right: -0.5rem;
        margin-left: -0.5rem;
    }

    .professional-form .row > [class*="col-"] {
        padding-right: 0.5rem;
        padding-left: 0.5rem;
    }
}

/* RTL Support */
[dir="rtl"] .professional-form .input-group .form-control {
    border-radius: 8px 0 0 8px;
}

[dir="rtl"] .professional-form .form-check {
    padding-right: 1.8rem;
    padding-left: 0;
}

[dir="rtl"] .professional-form .form-check-input {
    margin-right: -1.8rem;
    margin-left: 0;
}

/* Animation Effects */
.professional-form .form-control:focus,
.professional-form .form-select:focus {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.4);
    }
    70% {
        box-shadow: 0 0 0 5px rgba(52, 152, 219, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(52, 152, 219, 0);
    }
}

/* Form with Background Image */
.professional-form.with-bg {
    background-image: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.95) 100%);
    background-size: cover;
    background-position: center;
}

[data-theme="dark"] .professional-form.with-bg {
    background-image: linear-gradient(135deg, rgba(52, 73, 94, 0.9) 0%, rgba(44, 62, 80, 0.95) 100%);
}

/* Form with Icon */
.professional-form .input-icon {
    position: relative;
}

.professional-form .input-icon .form-control {
    padding-left: 3rem;
}

.professional-form .input-icon i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--form-text-light);
    transition: all 0.3s ease;
    z-index: 2;
}

.professional-form .input-icon .form-control:focus + i {
    color: var(--form-primary);
}

[dir="rtl"] .professional-form .input-icon .form-control {
    padding-right: 3rem;
    padding-left: 1rem;
}

[dir="rtl"] .professional-form .input-icon i {
    right: 1rem;
    left: auto;
}

/* Form with Floating Labels */
.professional-form.floating-labels .form-group {
    position: relative;
    margin-bottom: 2rem;
}

.professional-form.floating-labels .form-control {
    padding: 1.5rem 1rem 0.5rem;
    height: calc(3.5rem + 2px);
}

.professional-form.floating-labels label {
    position: absolute;
    top: 0;
    left: 0;
    padding: 1rem;
    transition: all 0.3s ease;
    opacity: 0.7;
}

.professional-form.floating-labels .form-control:focus ~ label,
.professional-form.floating-labels .form-control:not(:placeholder-shown) ~ label {
    transform: translateY(-0.5rem) scale(0.85);
    opacity: 1;
    color: var(--form-primary);
}

/* Form with Gradient Buttons */
.professional-form .btn-gradient {
    background-image: linear-gradient(135deg, var(--form-primary) 0%, #9b59b6 100%);
    border: none;
}

.professional-form .btn-gradient:hover {
    background-image: linear-gradient(135deg, #2980b9 0%, #8e44ad 100%);
}

/* Form with Animated Submit Button */
.professional-form .btn-animated {
    position: relative;
    overflow: hidden;
}

.professional-form .btn-animated::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.6s ease;
}

.professional-form .btn-animated:hover::before {
    left: 100%;
}
